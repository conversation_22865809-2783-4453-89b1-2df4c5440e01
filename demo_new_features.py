#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示大小预测分析窗口的新功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import json
import os
from datetime import datetime

def create_demo_window():
    """创建演示窗口"""
    root = tk.Tk()
    root.title("大小预测分析新功能演示")
    root.geometry("800x600")
    
    # 创建主框架
    main_frame = ttk.Frame(root, padding="10")
    main_frame.pack(fill='both', expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="大小预测分析新功能演示", 
                           font=("微软雅黑", 16, "bold"))
    title_label.pack(pady=10)
    
    # 功能说明
    info_text = """
新增功能说明：

1. 预测数据库按钮
   - 自动分析每位(一位到五位)每个数字(0-9)的最佳预测组合
   - 将准确率最高的组合保存到SQLite数据库(plwdx.db)
   - 支持进度显示和中途停止
   - 如果数据库已存在，只添加缺失的记录

2. 检验组合按钮  
   - 使用数据库中的最佳组合对历史数据进行预测验证
   - 计算并显示每期预测的成功率
   - 统计各位置的总体成功率
   - 支持进度显示和中途停止

数据库结构：
- 表名：prediction_combinations
- 字段：位置、数字、组合描述、组合数据、准确率、正确次数、总次数、创建时间
- 唯一约束：(位置, 数字) 确保每个位置-数字组合只有一条最佳记录
"""
    
    info_label = ttk.Label(main_frame, text=info_text, justify='left', 
                          font=("微软雅黑", 10))
    info_label.pack(pady=10, anchor='w')
    
    # 按钮框架
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(pady=20)
    
    def show_database_info():
        """显示数据库信息"""
        db_file = 'plwdx.db'
        
        if not os.path.exists(db_file):
            messagebox.showinfo("数据库信息", "数据库文件不存在。\n请先在大小预测分析窗口中点击'预测数据库'按钮创建数据库。")
            return
        
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 获取记录总数
            cursor.execute('SELECT COUNT(*) FROM prediction_combinations')
            total_count = cursor.fetchone()[0]
            
            # 获取各位置的记录数
            cursor.execute('SELECT position, COUNT(*) FROM prediction_combinations GROUP BY position')
            position_counts = cursor.fetchall()
            
            # 获取平均准确率
            cursor.execute('SELECT AVG(accuracy) FROM prediction_combinations')
            avg_accuracy = cursor.fetchone()[0]
            
            # 获取最高准确率记录
            cursor.execute('SELECT position, number, combination_desc, accuracy FROM prediction_combinations ORDER BY accuracy DESC LIMIT 5')
            top_records = cursor.fetchall()
            
            conn.close()
            
            # 构建信息文本
            info = f"数据库信息 (plwdx.db):\n\n"
            info += f"总记录数: {total_count}\n"
            info += f"平均准确率: {avg_accuracy:.2f}%\n\n"
            
            info += "各位置记录数:\n"
            for pos, count in position_counts:
                info += f"  {pos}: {count} 条\n"
            
            info += "\n准确率最高的5个组合:\n"
            for i, (pos, num, desc, acc) in enumerate(top_records, 1):
                info += f"  {i}. {pos}数字{num}: {desc} ({acc:.2f}%)\n"
            
            messagebox.showinfo("数据库信息", info)
            
        except Exception as e:
            messagebox.showerror("错误", f"读取数据库时出错：{str(e)}")
    
    def create_sample_database():
        """创建示例数据库"""
        db_file = 'plwdx.db'
        
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 创建表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS prediction_combinations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    position TEXT NOT NULL,
                    number INTEGER NOT NULL,
                    combination_desc TEXT NOT NULL,
                    combination_data TEXT NOT NULL,
                    accuracy REAL NOT NULL,
                    correct_count INTEGER NOT NULL,
                    total_count INTEGER NOT NULL,
                    created_date TEXT NOT NULL,
                    UNIQUE(position, number)
                )
            ''')
            
            # 示例数据
            sample_data = []
            positions = ['一位', '二位', '三位', '四位', '五位']
            
            import random
            for pos in positions:
                for num in range(10):
                    # 生成随机的组合数据
                    combo_data = [(random.randint(1, 5), random.randint(2, 6)) for _ in range(random.randint(2, 4))]
                    combo_desc = '+'.join([f"上{p}期{['一','二','三','四','五'][c-2]}" for p, c in combo_data])
                    
                    accuracy = random.uniform(45.0, 85.0)
                    total = random.randint(15, 30)
                    correct = int(total * accuracy / 100)
                    
                    sample_data.append((
                        pos, num, combo_desc, json.dumps(combo_data),
                        accuracy, correct, total, datetime.now().isoformat()
                    ))
            
            # 插入数据
            cursor.executemany('''
                INSERT OR REPLACE INTO prediction_combinations 
                (position, number, combination_desc, combination_data, accuracy, correct_count, total_count, created_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', sample_data)
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("成功", f"已创建示例数据库，包含 {len(sample_data)} 条记录。")
            
        except Exception as e:
            messagebox.showerror("错误", f"创建示例数据库时出错：{str(e)}")
    
    def delete_database():
        """删除数据库文件"""
        db_file = 'plwdx.db'
        
        if os.path.exists(db_file):
            if messagebox.askyesno("确认", "确定要删除数据库文件吗？"):
                try:
                    os.remove(db_file)
                    messagebox.showinfo("成功", "数据库文件已删除。")
                except Exception as e:
                    messagebox.showerror("错误", f"删除数据库文件时出错：{str(e)}")
        else:
            messagebox.showinfo("提示", "数据库文件不存在。")
    
    # 创建按钮
    ttk.Button(button_frame, text="查看数据库信息", command=show_database_info).pack(side='left', padx=5)
    ttk.Button(button_frame, text="创建示例数据库", command=create_sample_database).pack(side='left', padx=5)
    ttk.Button(button_frame, text="删除数据库", command=delete_database).pack(side='left', padx=5)
    
    # 使用说明
    usage_text = """
使用步骤：

1. 首先运行主程序 (排列5分析软件精简版.py)
2. 下载或导入彩票数据
3. 点击"大小预测"按钮打开大小预测分析窗口
4. 设置分析参数（期号范围、位数等）
5. 点击"预测数据库"按钮创建预测数据库（这会分析所有位置和数字的最佳组合）
6. 创建完成后，点击"检验组合"按钮验证预测效果

注意：
- 预测数据库创建过程可能需要较长时间，请耐心等待
- 可以随时点击"停止分析"按钮中断操作
- 数据库文件保存为 plwdx.db，可以重复使用
"""
    
    usage_label = ttk.Label(main_frame, text=usage_text, justify='left', 
                           font=("微软雅黑", 9), foreground="blue")
    usage_label.pack(pady=10, anchor='w')
    
    # 关闭按钮
    ttk.Button(main_frame, text="关闭", command=root.destroy).pack(pady=20)
    
    return root

if __name__ == "__main__":
    root = create_demo_window()
    root.mainloop()
