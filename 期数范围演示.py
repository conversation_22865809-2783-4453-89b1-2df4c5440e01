#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示期数范围对预测数据库分析结果的影响
"""

import tkinter as tk
from tkinter import ttk, messagebox
import random
from datetime import datetime, timedelta

def create_demo_data(periods=500):
    """创建演示用的彩票数据"""
    data = []
    start_date = datetime(2020, 1, 1)
    
    for i in range(periods):
        period_num = 20001 + i
        date = start_date + timedelta(days=i)
        
        # 生成随机的5位数字
        numbers = [random.randint(0, 9) for _ in range(5)]
        
        data.append({
            'period': period_num,
            'date': date.strftime('%Y-%m-%d'),
            'numbers': numbers
        })
    
    return data

def analyze_period_range_impact():
    """分析不同期数范围对结果的影响"""
    
    # 创建演示数据
    demo_data = create_demo_data(500)
    
    # 模拟不同期数范围的分析
    ranges = [
        (1, 50, "最近50期"),
        (1, 100, "最近100期"), 
        (1, 200, "最近200期"),
        (1, 300, "最近300期"),
        (50, 200, "最近50-200期"),
        (100, 300, "最近100-300期")
    ]
    
    results = []
    
    for start, end, desc in ranges:
        # 模拟分析过程
        analysis_data = demo_data[-end:-start+1] if start > 1 else demo_data[-end:]
        
        # 模拟找到的最佳组合（随机生成用于演示）
        best_accuracy = random.uniform(55.0, 85.0)
        sample_count = len(analysis_data) // 10  # 假设每10期出现一次目标数字
        correct_count = int(sample_count * best_accuracy / 100)
        
        combo_desc = f"上{random.randint(1,3)}期{random.choice(['一','二','三','四','五'])}位+上{random.randint(1,5)}期{random.choice(['一','二','三','四','五'])}位"
        
        results.append({
            'range_desc': desc,
            'data_count': len(analysis_data),
            'combo_desc': combo_desc,
            'accuracy': best_accuracy,
            'correct': correct_count,
            'total': sample_count
        })
    
    return results

def create_demo_window():
    """创建演示窗口"""
    root = tk.Tk()
    root.title("期数范围对预测分析的影响演示")
    root.geometry("900x700")
    
    # 创建主框架
    main_frame = ttk.Frame(root, padding="10")
    main_frame.pack(fill='both', expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="期数范围对预测分析的影响演示", 
                           font=("微软雅黑", 16, "bold"))
    title_label.pack(pady=10)
    
    # 说明文本
    info_text = """
重要说明：预测数据库功能分析的是用户指定期数范围内的数据

不同的期数范围会产生不同的分析结果：
• 期数范围越大，数据样本越多，统计结果可能更稳定
• 期数范围越小，更能反映近期的数据特征
• 不同的期数范围可能找到不同的最佳预测组合
• 用户可以根据需要选择合适的期数范围进行分析

下面的演示展示了不同期数范围对分析结果的影响：
"""
    
    info_label = ttk.Label(main_frame, text=info_text, justify='left', 
                          font=("微软雅黑", 10))
    info_label.pack(pady=10, anchor='w')
    
    # 创建结果显示区域
    result_frame = ttk.LabelFrame(main_frame, text="不同期数范围的分析结果对比", padding="10")
    result_frame.pack(fill='both', expand=True, pady=10)
    
    # 创建表格
    columns = ('期数范围', '数据量', '最佳组合', '准确率', '正确/总数')
    tree = ttk.Treeview(result_frame, columns=columns, show='headings', height=8)
    
    # 设置列标题
    for col in columns:
        tree.heading(col, text=col)
        tree.column(col, width=150)
    
    # 添加滚动条
    scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=tree.yview)
    tree.configure(yscrollcommand=scrollbar.set)
    
    tree.pack(side='left', fill='both', expand=True)
    scrollbar.pack(side='right', fill='y')
    
    def run_analysis():
        """运行分析演示"""
        # 清空现有数据
        for item in tree.get_children():
            tree.delete(item)
        
        # 获取分析结果
        results = analyze_period_range_impact()
        
        # 显示结果
        for result in results:
            tree.insert('', 'end', values=(
                result['range_desc'],
                f"{result['data_count']}期",
                result['combo_desc'],
                f"{result['accuracy']:.1f}%",
                f"{result['correct']}/{result['total']}"
            ))
        
        messagebox.showinfo("分析完成", "演示分析已完成！\n\n可以看到不同期数范围产生了不同的最佳组合和准确率。")
    
    # 按钮框架
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(pady=20)
    
    ttk.Button(button_frame, text="运行演示分析", command=run_analysis).pack(side='left', padx=10)
    ttk.Button(button_frame, text="关闭", command=root.destroy).pack(side='left', padx=10)
    
    # 使用建议
    advice_text = """
使用建议：

1. 短期分析（最近50-100期）：
   - 适合捕捉近期数据特征
   - 反应最新的数据趋势
   - 样本量较小，结果可能不够稳定

2. 中期分析（最近100-300期）：
   - 平衡了样本量和时效性
   - 推荐的分析范围
   - 既有足够样本又不会过于陈旧

3. 长期分析（最近300期以上）：
   - 样本量大，统计结果更稳定
   - 可能包含过于陈旧的数据
   - 适合寻找长期规律

4. 自定义范围（如最近50-200期）：
   - 排除最近的异常数据
   - 专注于特定时间段的规律
   - 适合特殊分析需求

实际使用时，建议：
• 先用中期范围（如最近1-200期）建立基础数据库
• 根据需要用不同范围进行对比分析
• 定期更新数据库以包含最新数据
"""
    
    advice_label = ttk.Label(main_frame, text=advice_text, justify='left', 
                            font=("微软雅黑", 9), foreground="blue")
    advice_label.pack(pady=10, anchor='w')
    
    return root

if __name__ == "__main__":
    root = create_demo_window()
    root.mainloop()
