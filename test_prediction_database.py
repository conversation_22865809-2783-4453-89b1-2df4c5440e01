#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试预测数据库功能
"""

import sqlite3
import json
import os

def test_database_creation():
    """测试数据库创建功能"""
    db_file = 'plwdx.db'
    
    # 如果数据库文件存在，先删除
    if os.path.exists(db_file):
        os.remove(db_file)
        print(f"已删除现有数据库文件: {db_file}")
    
    # 创建数据库连接
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    
    # 创建表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS prediction_combinations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            position TEXT NOT NULL,
            number INTEGER NOT NULL,
            combination_desc TEXT NOT NULL,
            combination_data TEXT NOT NULL,
            accuracy REAL NOT NULL,
            correct_count INTEGER NOT NULL,
            total_count INTEGER NOT NULL,
            created_date TEXT NOT NULL,
            UNIQUE(position, number)
        )
    ''')
    
    print("数据库表创建成功")
    
    # 插入测试数据
    test_data = [
        ('一位', 0, '上1期二位+上2期三位', '[[1, 3], [2, 4]]', 75.5, 15, 20, '2024-01-01T10:00:00'),
        ('一位', 1, '上1期一位+上3期五位', '[[1, 2], [3, 6]]', 68.2, 12, 18, '2024-01-01T10:01:00'),
        ('二位', 0, '上2期四位+上1期三位', '[[2, 5], [1, 4]]', 72.1, 18, 25, '2024-01-01T10:02:00'),
    ]
    
    for data in test_data:
        cursor.execute('''
            INSERT INTO prediction_combinations 
            (position, number, combination_desc, combination_data, accuracy, correct_count, total_count, created_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', data)
    
    conn.commit()
    print(f"插入了 {len(test_data)} 条测试数据")
    
    # 查询数据验证
    cursor.execute('SELECT * FROM prediction_combinations')
    records = cursor.fetchall()
    
    print("\n数据库中的记录:")
    for record in records:
        print(f"ID: {record[0]}, 位置: {record[1]}, 数字: {record[2]}")
        print(f"  组合描述: {record[3]}")
        print(f"  准确率: {record[5]:.1f}% ({record[6]}/{record[7]})")
        print(f"  创建时间: {record[8]}")
        print()
    
    conn.close()
    print(f"数据库测试完成，文件保存为: {db_file}")

def test_database_query():
    """测试数据库查询功能"""
    db_file = 'plwdx.db'
    
    if not os.path.exists(db_file):
        print("数据库文件不存在，请先运行 test_database_creation()")
        return
    
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    
    # 查询特定位置和数字的组合
    position = '一位'
    number = 0
    
    cursor.execute('''
        SELECT combination_desc, combination_data, accuracy, correct_count, total_count 
        FROM prediction_combinations 
        WHERE position = ? AND number = ?
    ''', (position, number))
    
    result = cursor.fetchone()
    
    if result:
        print(f"查询 {position} 数字 {number} 的最佳组合:")
        print(f"  组合描述: {result[0]}")
        print(f"  组合数据: {result[1]}")
        print(f"  准确率: {result[2]:.1f}% ({result[3]}/{result[4]})")
        
        # 解析组合数据
        combination_data = json.loads(result[1])
        print(f"  解析后的组合: {combination_data}")
    else:
        print(f"未找到 {position} 数字 {number} 的组合记录")
    
    conn.close()

def test_database_update():
    """测试数据库更新功能"""
    db_file = 'plwdx.db'
    
    if not os.path.exists(db_file):
        print("数据库文件不存在，请先运行 test_database_creation()")
        return
    
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    
    # 更新现有记录
    position = '一位'
    number = 0
    new_accuracy = 80.5
    new_correct = 16
    new_total = 20
    
    cursor.execute('''
        UPDATE prediction_combinations 
        SET accuracy = ?, correct_count = ?, total_count = ?
        WHERE position = ? AND number = ?
    ''', (new_accuracy, new_correct, new_total, position, number))
    
    if cursor.rowcount > 0:
        print(f"成功更新 {position} 数字 {number} 的记录")
        print(f"  新准确率: {new_accuracy}% ({new_correct}/{new_total})")
    else:
        print(f"未找到要更新的记录: {position} 数字 {number}")
    
    conn.commit()
    conn.close()

if __name__ == "__main__":
    print("=== 测试预测数据库功能 ===\n")
    
    print("1. 测试数据库创建...")
    test_database_creation()
    
    print("\n2. 测试数据库查询...")
    test_database_query()
    
    print("\n3. 测试数据库更新...")
    test_database_update()
    
    print("\n4. 再次查询验证更新...")
    test_database_query()
    
    print("\n=== 测试完成 ===")
