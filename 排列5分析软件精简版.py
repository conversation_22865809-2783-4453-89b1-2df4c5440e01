import tkinter as tk
from tkinter import filedialog, messagebox
import pandas as pd
from tkinter import ttk
from ttkthemes import ThemedTk, ThemedStyle
import requests
from io import StringIO
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import re
import json
import os
import numpy as np
from PIL import Image, ImageTk
import time
import webbrowser
import datetime

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

class Arrangement5LotteryApp:
    def __init__(self, root):
        self.root = root
        self.root.title("中道体彩排列5分析软件")

        # 配置基础样式
        self.style = ThemedStyle(root)
        self.style.set_theme("vista")  # 使用vista主题，响应更快

        # 配置字体和行高
        self.style.configure(".", font=("微软雅黑", 11))
        self.style.configure("Treeview", rowheight=25)
        self.style.configure("Treeview.Heading", font=("微软雅黑", 10, "bold"))

        # 设置窗口最小尺寸
        self.root.minsize(1024, 768)

        # 定义配置文件路径
        self.config_file = "column_widths.json"
        self.last_dir_file = "last_directory.json"
        self.main_window_size_file = "main_window_size.json"

        # 添加数据历史栈，最多保存10个历史状态
        self.data_history = []
        self.current_data = []
        self.current_state = {}  # 用于存储当前状态
        self.history_position = -1  # 当前在历史栈中的位置，-1表示不在历史中

        # 加载保存的列宽度和最后导入目录
        self.load_saved_widths()
        self.load_last_directory()

        # 加载上次保存的主窗口大小和位置
        self.load_main_window_size()

        # 创建菜单栏
        self._create_menu_bar()

        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # 数据展示区
        self.data_frame = ttk.LabelFrame(main_frame, text="开奖数据")
        self.data_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # 创建Treeview和滚动条
        tree_frame = ttk.Frame(self.data_frame)
        tree_frame.pack(fill='both', expand=True)

        # 定义列
        columns = [
            '期号', '开奖日期', '一位', '二位', '三位', '四位', '五位',
            '和值', '和值尾', '跨度',
            '奇个数', '大个数', '组数', '同个数', '期号尾奇', '期号尾大', '序号', '遗漏', '唯一号位数', '唯一号值', '相同号值', '奇偶组合', '大小组合'
        ]

        # 设置列标题和宽度
        column_widths = {
            '期号': 80, '开奖日期': 100,
            '一位': 40, '二位': 40, '三位': 40, '四位': 40, '五位': 40,
            '和值': 60, '和值尾': 80, '跨度': 80,
            '奇个数': 60, '大个数': 60, '组数': 60, '同个数': 60, '期号尾奇': 80, '期号尾大': 80,
            '序号': 60, '遗漏': 60, '唯一号位数': 80, '唯一号值': 80, '相同号值': 80,
            '奇偶组合': 120, '大小组合': 120
        }

        # 创建Treeview
        self.tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=20)

        # 首先计算每列所需的最小宽度
        min_widths = {}
        for col in columns:
            # 设置最小宽度为40像素
            min_widths[col] = 40

        # 设置列属性
        for col in columns:
            self.tree.heading(col, text=col)
            saved_width = self.saved_widths.get(col)
            # 使用保存的宽度或默认宽度
            col_width = saved_width if saved_width else column_widths.get(col, 100)
            self.tree.column(col, width=col_width, minwidth=min_widths[col])

        # 创建搜索条件输入框
        self.search_entries = {}
        for col in columns:
            # 创建标题标签
            label = ttk.Label(tree_frame, text=col)
            label.grid(row=0, column=columns.index(col), padx=1, pady=1, sticky='ew')

            # 获取标题文字的实际宽度
            label.update_idletasks()
            text_width = label.winfo_reqwidth()

            # 创建搜索输入框，宽度与标题文字宽度一致
            entry = ttk.Entry(tree_frame)
            entry.grid(row=1, column=columns.index(col), padx=1, pady=1, sticky='ew')
            # 将像素宽度转换为字符宽度（假设每个字符平均8像素）
            char_width = max(text_width // 8, len(col))
            entry.configure(width=char_width)
            self.search_entries[col] = entry

            # 配置列的权重和最小宽度
            tree_frame.grid_columnconfigure(columns.index(col), weight=1, minsize=text_width)

        # 添加滚动条
        vsb = ttk.Scrollbar(tree_frame, orient="vertical", command=self.tree.yview)
        hsb = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)

        # 放置Treeview和滚动条
        self.tree.grid(row=2, column=0, columnspan=len(columns), sticky='nsew')
        vsb.grid(row=2, column=len(columns), sticky='ns')
        hsb.grid(row=3, column=0, columnspan=len(columns), sticky='ew')

        tree_frame.grid_rowconfigure(2, weight=1)

        # 绑定列宽度改变事件
        self.tree.bind('<Configure>', self.on_column_configure)

        # 绑定右键菜单事件
        self.tree.bind('<Button-3>', self.show_context_menu)

        # 创建右键菜单
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="复制", command=self.copy_selected)
        self.context_menu.add_command(label="删除", command=self.delete_selected)
        self.context_menu.add_command(label="显示选中行", command=self.analyze_selected_rows)

        # 创建底部按钮区域
        bottom_frame = ttk.Frame(self.root)
        bottom_frame.pack(fill='x', padx=10, pady=5)

        # 创建一个居中的框架
        center_frame = ttk.Frame(bottom_frame)
        center_frame.pack(expand=True)

        # 创建三个按钮组框架
        data_group = ttk.LabelFrame(center_frame, text="数据管理", padding=5)
        data_group.pack(side='left', padx=5)

        analysis_group = ttk.LabelFrame(center_frame, text="分析工具", padding=5)
        analysis_group.pack(side='left', padx=5)

        search_group = ttk.LabelFrame(center_frame, text="搜索功能", padding=5)
        search_group.pack(side='left', padx=5)

        # 数据管理组按钮
        ttk.Button(data_group, text="数据下载", command=self.download_data, width=8).pack(side='left', padx=2)
        ttk.Button(data_group, text="导入号码", command=self.import_data, width=8).pack(side='left', padx=2)
        ttk.Button(data_group, text="导出号码", command=self.save_results, width=8).pack(side='left', padx=2)

        # 分析工具组按钮
        ttk.Button(analysis_group, text="遗漏计算", command=self.calculate_missing_numbers, width=8).pack(side='left', padx=2)
        ttk.Button(analysis_group, text="大小预测", command=self.show_size_prediction, width=8).pack(side='left', padx=2)
        # 删除组合分析和异号分析按钮
        # ttk.Button(analysis_group, text="组合分析", command=self.show_combination_analysis, width=8).pack(side='left', padx=2)
        # ttk.Button(analysis_group, text="异号分析", command=self.show_different_number_analysis, width=8).pack(side='left', padx=2)
        # ttk.Button(analysis_group, text="异同分析", command=self.show_difference_analysis, width=8).pack(side='left', padx=2)  # 已删除

        # 搜索功能组按钮和选项
        # 添加正则表达式搜索选项（放在搜索按钮左边）
        self.use_regex_var = tk.BooleanVar(value=False)
        regex_check = ttk.Checkbutton(search_group, text="正则表达式", variable=self.use_regex_var)
        regex_check.pack(side='left', padx=5)

        ttk.Button(search_group, text="搜索", command=self.search_data, width=8).pack(side='left', padx=2)
        # 删除高级搜索按钮
        # ttk.Button(search_group, text="高级搜索", command=self.show_advanced_search, width=8).pack(side='left', padx=2)
        ttk.Button(search_group, text="下期搜索", command=self.search_next_period, width=8).pack(side='left', padx=2)
        ttk.Button(search_group, text="返回", command=self.reset_view, width=8).pack(side='left', padx=2)
        ttk.Button(search_group, text="前进", command=self.forward_view, width=8).pack(side='left', padx=2)

        # 创建正则表达式提示工具
        self.create_tooltip(regex_check, "启用正则表达式搜索\n例如：\n- 使用 '2.*3' 搜索2和3之间的任意字符\n- 使用 '^2' 搜索以2开头的内容\n- 使用 '3$' 搜索以3结尾的内容")

        # 状态栏
        self.status_bar = ttk.Label(self.root, text="准备就绪", relief=tk.SUNKEN, anchor='w')
        self.status_bar.pack(side='bottom', fill='x', pady=(5,0))

        # 在关闭窗口时保存列宽度
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def _create_menu_bar(self):
        """创建菜单栏"""
        menu_bar = tk.Menu(self.root)
        self.root.config(menu=menu_bar)

        # 文件菜单
        file_menu = tk.Menu(menu_bar, tearoff=0)
        menu_bar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导入数据", command=self.import_data)
        file_menu.add_command(label="保存分析结果", command=self.save_results)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)

        # 编辑菜单
        edit_menu = tk.Menu(menu_bar, tearoff=0)
        menu_bar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="复制选中", command=self.copy_selected)
        edit_menu.add_command(label="删除选中", command=self.delete_selected)

        # 分析菜单
        analysis_menu = tk.Menu(menu_bar, tearoff=0)
        menu_bar.add_cascade(label="分析", menu=analysis_menu)
        # 删除组合分析和异号分析菜单项
        # analysis_menu.add_command(label="组合分析", command=self.show_combination_analysis)
        # analysis_menu.add_command(label="异号分析", command=self.show_different_number_analysis)
        # analysis_menu.add_command(label="异同分析", command=self.show_difference_analysis)  # 已删除
        analysis_menu.add_command(label="遗漏计算", command=self.calculate_missing_numbers)

        # 帮助菜单
        help_menu = tk.Menu(menu_bar, tearoff=0)
        menu_bar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_separator()
        help_menu.add_command(label="关于", command=self.show_about)

    def download_data(self):
        try:
            # 创建进度条窗口
            progress_window = tk.Toplevel(self.root)
            progress_window.title("下载进度")
            progress_window.geometry("300x160")

            # 设置窗口在屏幕中央
            progress_window.update_idletasks()
            width = progress_window.winfo_width()
            height = progress_window.winfo_height()
            x = (progress_window.winfo_screenwidth() // 2) - (width // 2)
            y = (progress_window.winfo_screenheight() // 2) - (height // 2)
            progress_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

            # 创建进度条框架
            progress_frame = ttk.Frame(progress_window, padding="20")
            progress_frame.pack(fill='both', expand=True)

            # 添加标签
            status_label = ttk.Label(progress_frame, text="正在连接服务器...")
            status_label.pack(pady=10)

            # 添加进度条
            progress_var = tk.DoubleVar()
            progress_bar = ttk.Progressbar(progress_frame, variable=progress_var, maximum=100)
            progress_bar.pack(fill='x', pady=10)

            # 添加进度文本标签
            progress_label = ttk.Label(progress_frame, text="0%")
            progress_label.pack(pady=5)

            # 更新UI
            progress_window.update()

            self.status_bar.config(text="正在从网址下载数据...")
            url = "http://data.17500.cn/pl5_asc.txt"
            print(f"开始下载数据，网址: {url}")

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
                'Connection': 'keep-alive',
                'Cache-Control': 'max-age=0'
            }

            try:
                # 使用流式请求
                response = requests.get(url, headers=headers, stream=True, timeout=30)
                print(f"响应状态码: {response.status_code}")
                response.raise_for_status()
            except requests.exceptions.RequestException as e:
                print(f"请求异常: {str(e)}")
                # 尝试使用备用URL
                try:
                    backup_url = "http://www.17500.cn/getData/pl5.TXT"
                    print(f"尝试备用URL: {backup_url}")
                    status_label.config(text="尝试备用数据源...")
                    progress_window.update()
                    response = requests.get(backup_url, headers=headers, stream=True, timeout=30)
                    print(f"备用URL响应状态码: {response.status_code}")
                    response.raise_for_status()
                except requests.exceptions.RequestException as e2:
                    print(f"备用URL请求异常: {str(e2)}")
                    raise Exception(f"主要URL和备用URL都无法访问: {str(e)}, {str(e2)}")

            # 获取总文件大小
            total_size = int(response.headers.get('content-length', 0))

            # 如果无法获取文件大小，显示不确定进度条
            if total_size == 0:
                progress_bar.config(mode='indeterminate')
                progress_bar.start()
                status_label.config(text="正在下载数据...")

            # 下载数据
            data = []
            downloaded_size = 0
            chunk_size = 8192  # 增大chunk_size提高下载效率

            for chunk in response.iter_content(chunk_size=chunk_size):
                if chunk:
                    data.append(chunk)
                    downloaded_size += len(chunk)

                    if total_size > 0:
                        # 计算下载进度
                        progress = (downloaded_size / total_size) * 100
                        progress_var.set(progress)
                        progress_label.config(text=f"{progress:.1f}%")
                        status_label.config(text="正在下载数据...")
                        progress_window.update()

            # 合并数据
            data = b''.join(data)

            # 简化解码过程
            try:
                # 尝试直接以gb2312解码
                content = data.decode('gb2312', errors='ignore')
            except:
                # 如果失败，尝试其他编码
                try:
                    content = data.decode('utf-8', errors='ignore')
                except:
                    content = data.decode('gbk', errors='ignore')

            status_label.config(text="正在处理数据...")
            progress_window.update()

            # 打印数据前几行
            lines = content.split('\n')
            for i in range(min(5, len(lines))):
                print(f"第{i+1}行: {lines[i]}")

            if not content.strip():
                raise Exception("下载的数据为空")

            # 处理数据
            self.process_data(content)

            # 更新状态
            status_label.config(text="下载完成！")
            progress_var.set(100)
            progress_label.config(text="100%")
            progress_window.update()

            # 关闭进度条窗口
            progress_window.destroy()

            self.status_bar.config(text=f"数据下载完成，共{len(self.tree.get_children())}条记录")

        except Exception as e:
            try:
                progress_window.destroy()
            except:
                pass
            messagebox.showerror("错误", f"下载数据时出错：{str(e)}")
            self.status_bar.config(text="下载数据失败")

    def process_data(self, data):
        # 保存原始数据用于返回功能
        self.original_data = []

        # 清空现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)

        if isinstance(data, str):
            # 按行分割，移除空行
            lines = [line.strip() for line in data.split('\n') if line.strip()]

            success_count = 0
            error_count = 0

            # 创建用于遗漏分析的数据结构
            all_data = []

            for line in lines:
                try:
                    # 直接按空格分割
                    parts = line.split()

                    if len(parts) >= 7:  # 确保至少有期号、日期、5个号码
                        # 只提取关键数据：期号、日期和5个号码
                        row_data = [
                            parts[0],  # 期号
                            parts[1],  # 日期
                            parts[2],  # 一位
                            parts[3],  # 二位
                            parts[4],  # 三位
                            parts[5],  # 四位
                            parts[6]   # 五位
                        ]
                        # 验证并处理数据
                        if self.validate_lottery_data(row_data):
                            self.analyze_and_insert_row(row_data)
                            success_count += 1
                            # 添加到全局数据中用于分析
                            all_data.append(row_data)
                except Exception as e:
                    error_count += 1
                    continue

            # 将处理好的数据保存到DataFrame中供遗漏分析使用
            if all_data:
                import pandas as pd
                self.data = pd.DataFrame(all_data)

            # 更新状态栏显示处理的记录数
            record_count = len(self.tree.get_children())
            self.status_bar.config(text=f"数据处理完成，共{record_count}条记录")

            # 保存所有数据用于返回功能
            for item in self.tree.get_children():
                self.original_data.append(self.tree.item(item))

            print(f"数据处理完成，成功: {success_count}, 失败: {error_count}")

    def validate_lottery_data(self, parts):
        """验证是否为有效的排列5数据"""
        try:
            if len(parts) < 7:  # 需要7个数据：期号、日期、5个号码
                print(f"数据字段不足: {parts}")
                return False

            # 验证期号格式（允许最多7位数字，允许前导零）
            issue_number = parts[0].strip()
            if not (issue_number.isdigit() and 1 <= int(issue_number) <= 9999999):
                print(f"期号格式错误: {issue_number}")
                return False

            # 验证日期格式 - 允许多种格式
            date_str = parts[1].strip()
            date_pattern = r'^(\d{4})[-/](\d{1,2})[-/](\d{1,2})$'
            date_match = re.match(date_pattern, date_str)
            if not date_match:
                # 尝试其他日期格式
                if re.match(r'^\d{8}$', date_str):  # 如果是8位数字，尝试解析为YYYYMMDD
                    try:
                        year = date_str[:4]
                        month = date_str[4:6]
                        day = date_str[6:8]
                        # 检查是否是有效日期
                        if 1 <= int(month) <= 12 and 1 <= int(day) <= 31:
                            # 这里不更新parts[1]，因为在analyze_and_insert_row中会标准化日期格式
                            pass
                        else:
                            print(f"日期格式错误: {date_str}")
                            return False
                    except:
                        print(f"日期格式错误: {date_str}")
                        return False
                else:
                    print(f"日期格式错误: {date_str}")
                    return False

            # 验证五个号码（0-9之间的数字）
            for i, num in enumerate(parts[2:7]):
                try:
                    n = int(num.strip())
                    if n < 0 or n > 9:
                        print(f"号码超出范围(0-9): 第{i+1}位={n}")
                        return False
                except ValueError as e:
                    print(f"号码不是有效数字: 第{i+1}位={num}, 错误: {str(e)}")
                    return False

            return True
        except Exception as e:
            print(f"验证数据时出错: {str(e)}")
            return False

    def get_number_properties(self, numbers):
        """获取字奇偶属性和编号"""
        result = []
        for num in numbers:
            if num % 2 == 0:
                result.append("偶")
            else:
                result.append("奇")
        pattern = "".join(result)
        # 获取奇偶组合的编号，五位数字共32种组合
        patterns = {
            "奇奇奇奇奇": 1, "奇奇奇奇偶": 2, "奇奇奇偶奇": 3, "奇奇奇偶偶": 4,
            "奇奇偶奇奇": 5, "奇奇偶奇偶": 6, "奇奇偶偶奇": 7, "奇奇偶偶偶": 8,
            "奇偶奇奇奇": 9, "奇偶奇奇偶": 10, "奇偶奇偶奇": 11, "奇偶奇偶偶": 12,
            "奇偶偶奇奇": 13, "奇偶偶奇偶": 14, "奇偶偶偶奇": 15, "奇偶偶偶偶": 16,
            "偶奇奇奇奇": 17, "偶奇奇奇偶": 18, "偶奇奇偶奇": 19, "偶奇奇偶偶": 20,
            "偶奇偶奇奇": 21, "偶奇偶奇偶": 22, "偶奇偶偶奇": 23, "偶奇偶偶偶": 24,
            "偶偶奇奇奇": 25, "偶偶奇奇偶": 26, "偶偶奇偶奇": 27, "偶偶奇偶偶": 28,
            "偶偶偶奇奇": 29, "偶偶偶奇偶": 30, "偶偶偶偶奇": 31, "偶偶偶偶偶": 32
        }
        return patterns.get(pattern, 0), pattern

    def get_size_combination(self, numbers):
        """获取大小组合和编号"""
        result = []
        for num in numbers:
            if num >= 5:
                result.append("大")
            else:
                result.append("小")
        pattern = "".join(result)
        # 获取大小组合编号
        patterns = {
            "大大大大大": 1, "大大大大小": 2, "大大大小大": 3, "大大大小小": 4,
            "大大小大大": 5, "大大小大小": 6, "大大小小大": 7, "大大小小小": 8,
            "大小大大大": 9, "大小大大小": 10, "大小大小大": 11, "大小大小小": 12,
            "大小小大大": 13, "大小小大小": 14, "大小小小大": 15, "大小小小小": 16,
            "小大大大大": 17, "小大大大小": 18, "小大大小大": 19, "小大大小小": 20,
            "小大小大大": 21, "小大小大小": 22, "小大小小大": 23, "小大小小小": 24,
            "小小大大大": 25, "小小大大小": 26, "小小大小大": 27, "小小大小小": 28,
            "小小小大大": 29, "小小小大小": 30, "小小小小大": 31, "小小小小小": 32
        }
        return patterns.get(pattern, 0), pattern

    def analyze_and_insert_row(self, row_data):
        try:
            if len(row_data) < 7:  # 期号、日期、5个号码
                print(f"Skip invalid data: {row_data}")
                return

            期号, 开奖日期, *numbers = row_data[:7]

            # 分析期号的十位和个位
            期号_int = int(期号)
            十位 = (期号_int // 10) % 10
            个位 = 期号_int % 10

            # 分析期号十位个位的奇偶
            十位奇偶 = "1" if 十位 % 2 != 0 else "0"
            个位奇偶 = "1" if 个位 % 2 != 0 else "0"
            期号尾奇 = f"{十位奇偶},{个位奇偶}"

            # 分析期号十位个位的大小
            十位大小 = "1" if 十位 >= 5 else "0"
            个位大小 = "1" if 个位 >= 5 else "0"
            期号尾大 = f"{十位大小},{个位大小}"

            # 标准化日期格式
            date_match = re.match(r'^(\d{4})[-/](\d{1,2})[-/](\d{1,2})$', 开奖日期.strip())
            if date_match:
                year, month, day = date_match.groups()
                开奖日期 = f"{year}-{month.zfill(2)}-{day.zfill(2)}"

            try:
                # 转换号码为整数
                numbers = [int(n) for n in numbers]

                # 基础统计（统计5个号码）
                全部号码 = numbers[:5]

                奇数个数 = sum(1 for x in 全部号码 if x % 2 != 0)
                大数个数 = sum(1 for x in 全部号码 if x >= 5)

                # 计算同号个数和相关统计
                号码计数 = {}
                for num in 全部号码:
                    号码计数[num] = 号码计数.get(num, 0) + 1

                # 统计组数（不同号码的个数）
                组数 = len(号码计数)

                # 统计同号个数（出现次数大于1的号码总次数）
                同号个数 = sum(count for count in 号码计数.values() if count > 1)

                # 统计唯一号的值（只出现一次的号码，按原始顺序）
                唯一号列表 = []
                相同号列表 = []
                唯一号位数列表 = []  # 新增：记录唯一号的位数
                for i, num in enumerate(全部号码, 1):  # 从1开始计数，表示位数
                    if 号码计数[num] == 1:
                        唯一号列表.append(str(num))
                        唯一号位数列表.append(str(i))  # 记录位数
                    elif num not in 相同号列表:  # 避免重复添加相同的号码
                        相同号列表.append(str(num))

                唯一号值 = ' '.join(唯一号列表)
                唯一号位数 = ' '.join(唯一号位数列表)  # 新增：唯一号位数字符串
                相同号值 = ' '.join(相同号列表)

                # 获取奇偶组合和大小组合
                奇偶组合编号, 奇偶组合 = self.get_number_properties(全部号码)
                大小组合编号, 大小组合 = self.get_size_combination(全部号码)

                # 计算和值、和值尾和跨度
                和值 = sum(全部号码)
                和值尾 = 和值 % 10

                # 计算ac值（号码的绝对差值之和）
                sorted_nums = sorted(全部号码)
                ac_value = sum(abs(sorted_nums[i] - sorted_nums[i-1]) for i in range(1, len(sorted_nums)))

                # 计算跨度（最大值与最小值的差）
                跨度 = max(全部号码) - min(全部号码)

                # 构建显示的号码列表
                display_numbers = [str(x) for x in 全部号码]

                values = (
                    期号, 开奖日期, *display_numbers,
                    和值, 和值尾, 跨度,
                    奇数个数, 大数个数, 组数, 同号个数, 期号尾奇, 期号尾大,
                    len(self.tree.get_children()) + 1, "", 唯一号位数, 唯一号值, 相同号值,
                    f"{str(奇偶组合编号).zfill(2)}+{奇偶组合}", f"{str(大小组合编号).zfill(2)}+{大小组合}"
                )

                self.tree.insert('', 'end', values=values)
            except ValueError as e:
                print(f"Invalid number format: {numbers}")
                print(f"Error details: {str(e)}")
                return

        except Exception as e:
            print(f"Error analyzing row: {row_data}")
            print(f"Error details: {str(e)}")

    def delete_selected(self):
        selected_items = self.tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要删除的记录")
            return

        if messagebox.askyesno("确认", "确定要删除选中的记录吗？"):
            for item in selected_items:
                self.tree.delete(item)
            self.status_bar.config(text="已删除选中记录")

    def save_results(self):
        file_path = filedialog.asksaveasfilename(
            defaultextension='.xlsx',
            filetypes=[('Excel files', '*.xlsx'), ('Text files', '*.txt')]
        )
        if not file_path:
            return

        try:
            data = []
            for item in self.tree.get_children():
                data.append(self.tree.item(item)['values'])

            df = pd.DataFrame(data, columns=[col for col in self.tree['columns']])

            if file_path.endswith('.xlsx'):
                df.to_excel(file_path, index=False)
            else:
                df.to_csv(file_path, index=False)

            messagebox.showinfo("成功", "数据导出成功！")
            self.status_bar.config(text="数据导出完成")
        except Exception as e:
            messagebox.showerror("错误", f"导出数据时出错：{str(e)}")
            self.status_bar.config(text="导出数据失败")

# 已移动到更新后的save_current_state方法中

    def search_data(self):
        """搜索功能"""
        try:
            # 在搜索前保存当前状态
            self.save_current_state()

            # 获取所有搜索条件
            search_conditions = {}
            for col, entry in self.search_entries.items():
                value = entry.get().strip()
                if value:  # 只添加非空的搜索条件
                    search_conditions[col] = value

            if not search_conditions:
                # 如果没有搜索条件，显示所有记录
                self.reset_view()
                return

            # 获取正则表达式搜索选项状态
            use_regex = getattr(self, 'use_regex_var', tk.BooleanVar()).get()

            # 获取所有数据
            all_items = self.tree.get_children()
            matched_items = []

            # 遍历所有行
            for item in all_items:
                values = self.tree.item(item)['values']
                match = True

                # 检查每个搜索条件
                for col, search_value in search_conditions.items():
                    col_idx = self.tree['columns'].index(col)
                    item_value = str(values[col_idx])

                    # 对数字类型的列进行值比较
                    if col in ['期号', '一位', '二位', '三位', '四位', '五位', '六位', '七位',
                             '奇个数', '大个数', '组数', '同个数']:
                        try:
                            # 支持比较运算符：>, <, >=, <=, =
                            if search_value.startswith('>='):
                                if float(item_value) < float(search_value[2:]):
                                    match = False
                                    break
                            elif search_value.startswith('<='):
                                if float(item_value) > float(search_value[2:]):
                                    match = False
                                    break
                            elif search_value.startswith('>'):
                                if float(item_value) <= float(search_value[1:]):
                                    match = False
                                    break
                            elif search_value.startswith('<'):
                                if float(item_value) >= float(search_value[1:]):
                                    match = False
                                    break
                            elif search_value.startswith('='):
                                if float(item_value) != float(search_value[1:]):
                                    match = False
                                    break
                            else:  # 默认为精确匹配
                                if float(item_value) != float(search_value):
                                    match = False
                                    break
                        except ValueError:
                            match = False
                            break
                    # 对于文本类型的列，进行文本匹配
                    else:
                        if use_regex:
                            try:
                                # 使用正则表达式匹配
                                if not re.search(search_value, item_value, re.IGNORECASE):
                                    match = False
                                    break
                            except re.error:
                                # 如果正则表达式有错误，回退到普通搜索
                                if search_value.lower() not in item_value.lower():
                                    match = False
                                    break
                        else:
                            # 普通文本搜索（不分大小写）
                            if search_value.lower() not in item_value.lower():
                                match = False
                                break

                if match:
                    matched_items.append(item)

            # 隐藏不匹配的行
            for item in all_items:
                if item not in matched_items:
                    self.tree.detach(item)  # 隐藏不匹配的行

            # 更新状态栏
            self.status_bar.config(text=f"找到 {len(matched_items)} 条匹配记录")

        except Exception as e:
            messagebox.showerror("错误", f"搜索时出错：{str(e)}")
            self.status_bar.config(text="搜索失败")

    def search_next_period(self):
        """下期搜索功能，搜索符合当前参数的下一期数据"""
        try:
            # 在搜索前保存当前状态
            self.save_current_state()

            # 获取所有搜索条件
            search_conditions = {}
            for col, entry in self.search_entries.items():
                value = entry.get().strip()
                if value:  # 只添加非空的搜索条件
                    search_conditions[col] = value

            if not search_conditions:
                messagebox.showinfo("提示", "请先输入搜索条件")
                return

            # 获取正则表达式搜索选项状态
            use_regex = getattr(self, 'use_regex_var', tk.BooleanVar()).get()

            # 获取所有数据
            all_items = self.tree.get_children()
            if not all_items:
                messagebox.showinfo("提示", "没有可搜索的数据")
                return

            # 先找出符合当前搜索条件的数据
            matched_items = []
            for item in all_items:
                values = self.tree.item(item)['values']
                match = True

                # 检查每个搜索条件
                for col, search_value in search_conditions.items():
                    col_idx = self.tree['columns'].index(col)
                    item_value = str(values[col_idx])

                    # 对数字类型的列进行值比较
                    if col in ['期号', '一位', '二位', '三位', '四位', '五位', '六位', '七位',
                             '奇个数', '大个数', '组数', '同个数']:
                        try:
                            # 支持比较运算符：>, <, >=, <=, =
                            if search_value.startswith('>='):
                                if float(item_value) < float(search_value[2:]):
                                    match = False
                                    break
                            elif search_value.startswith('<='):
                                if float(item_value) > float(search_value[2:]):
                                    match = False
                                    break
                            elif search_value.startswith('>'):
                                if float(item_value) <= float(search_value[1:]):
                                    match = False
                                    break
                            elif search_value.startswith('<'):
                                if float(item_value) >= float(search_value[1:]):
                                    match = False
                                    break
                            elif search_value.startswith('='):
                                if float(item_value) != float(search_value[1:]):
                                    match = False
                                    break
                            else:  # 默认为精确匹配
                                if float(item_value) != float(search_value):
                                    match = False
                                    break
                        except ValueError:
                            match = False
                            break
                    # 对于文本类型的列，进行文本匹配
                    else:
                        if use_regex:
                            try:
                                # 使用正则表达式匹配
                                if not re.search(search_value, item_value, re.IGNORECASE):
                                    match = False
                                    break
                            except re.error:
                                # 如果正则表达式有错误，回退到普通搜索
                                if search_value.lower() not in item_value.lower():
                                    match = False
                                    break
                        else:
                            # 普通文本搜索（不分大小写）
                            if search_value.lower() not in item_value.lower():
                                match = False
                                break

                if match:
                    matched_items.append(item)

            if not matched_items:
                messagebox.showinfo("提示", "没有找到符合条件的数据")
                return

            # 找出所有匹配项的下一期数据
            next_period_items = []
            for i, item in enumerate(all_items):
                if item in matched_items and i + 1 < len(all_items):
                    next_period_items.append(all_items[i + 1])

            if not next_period_items:
                messagebox.showinfo("提示", "没有找到符合条件的下一期数据")
                return

            # 清空当前显示
            for item in all_items:
                self.tree.detach(item)  # 隐藏所有行

            # 显示下一期数据
            for item in next_period_items:
                self.tree.reattach(item, '', 'end')  # 重新显示下一期数据

            # 更新状态栏
            self.status_bar.config(text=f"找到 {len(next_period_items)} 条符合条件的下一期数据")

        except Exception as e:
            messagebox.showerror("错误", f"下期搜索时出错：{str(e)}")
            self.status_bar.config(text="下期搜索失败")

    def save_current_state(self):
        """保存当前状态以便后续恢复"""
        try:
            # 保存当前显示的数据项
            visible_items = []
            for item in self.tree.get_children():
                visible_items.append(item)

            # 保存搜索框的当前值
            search_values = {}
            for col, entry in self.search_entries.items():
                search_values[col] = entry.get()

            # 存储到当前状态
            self.current_state = {
                'visible_items': visible_items,
                'search_values': search_values
            }

            # 如果当前在历史中间位置，则删除该位置之后的所有历史
            if self.history_position >= 0 and self.history_position < len(self.data_history) - 1:
                self.data_history = self.data_history[:self.history_position + 1]

            # 将当前状态保存到历史栈
            current_items = []
            for item in self.tree.get_children():
                current_items.append({
                    'values': self.tree.item(item)['values'],
                    'tags': self.tree.item(item)['tags']
                })

            if current_items:  # 只在有数据时保存状态
                # 限制历史栈最多保存10个状态
                if len(self.data_history) >= 10:
                    # 移除最旧的历史记录
                    self.data_history.pop(0)

                # 添加新的历史记录
                self.data_history.append(current_items)
                self.current_data = current_items
                # 更新历史位置为最新
                self.history_position = len(self.data_history) - 1
        except Exception as e:
            print(f"保存当前状态时出错: {str(e)}")

    def reset_view(self):
        """重置视图，返回到上一次操作前的状态"""
        try:
            # 清空所有搜索框
            for entry in self.search_entries.values():
                entry.delete(0, tk.END)

            # 如果有当前状态，先尝试恢复当前状态
            if self.current_state and 'visible_items' in self.current_state:
                # 获取所有数据项
                all_items = self.tree.get_children()

                # 隐藏所有行
                for item in all_items:
                    self.tree.detach(item)

                # 显示保存的可见项
                for item in self.current_state['visible_items']:
                    try:
                        self.tree.reattach(item, '', 'end')
                    except:
                        pass  # 如果项不存在，忽略错误

                # 恢复搜索框的值
                if 'search_values' in self.current_state:
                    for col, value in self.current_state['search_values'].items():
                        if col in self.search_entries:
                            self.search_entries[col].insert(0, value)

                # 清空当前状态
                self.current_state = {}

                # 更新状态栏
                self.status_bar.config(text="已恢复到上一次搜索状态")
                return

            # 如果没有当前状态或恢复失败，尝试从历史栈中恢复数据
            if self.data_history:
                # 如果当前位置有效，则移动到上一个历史位置
                if self.history_position > 0:
                    self.history_position -= 1
                    previous_data = self.data_history[self.history_position]

                    # 清空当前显示
                    for item in self.tree.get_children():
                        self.tree.delete(item)

                    # 恢复上一个状态的数据
                    for item_data in previous_data:
                        self.tree.insert('', 'end', values=item_data['values'], tags=item_data['tags'])

                    # 更新状态栏，显示当前历史位置
                    self.status_bar.config(text=f"已返回历史记录 {self.history_position + 1}/{len(self.data_history)}，共{len(previous_data)}条记录")
                else:
                    # 如果已经在最早的历史记录，提示用户
                    self.status_bar.config(text=f"已经是最早的历史记录 (1/{len(self.data_history)})")
            else:
                # 如果没有历史数据，恢复到当前数据
                if self.current_data:
                    # 清空当前显示
                    for item in self.tree.get_children():
                        self.tree.delete(item)

                    # 恢复当前数据
                    for item_data in self.current_data:
                        self.tree.insert('', 'end', values=item_data['values'], tags=item_data['tags'])

                    self.status_bar.config(text=f"已恢复当前数据，共{len(self.current_data)}条记录")
                else:
                    self.status_bar.config(text="没有历史数据可以恢复")
        except Exception as e:
            print(f"Error in reset_view: {str(e)}")
            messagebox.showerror("错误", "返回上一状态时出错")

    def forward_view(self):
        """前进到下一个历史状态"""
        try:
            # 清空所有搜索框
            for entry in self.search_entries.values():
                entry.delete(0, tk.END)

            # 检查是否有历史数据和是否可以前进
            if self.data_history and self.history_position < len(self.data_history) - 1:
                # 移动到下一个历史位置
                self.history_position += 1
                next_data = self.data_history[self.history_position]

                # 清空当前显示
                for item in self.tree.get_children():
                    self.tree.delete(item)

                # 恢复下一个状态的数据
                for item_data in next_data:
                    self.tree.insert('', 'end', values=item_data['values'], tags=item_data['tags'])

                # 更新状态栏，显示当前历史位置
                self.status_bar.config(text=f"已前进到历史记录 {self.history_position + 1}/{len(self.data_history)}，共{len(next_data)}条记录")
            else:
                # 如果已经在最新的历史记录，提示用户
                if self.data_history:
                    self.status_bar.config(text=f"已经是最新的历史记录 ({len(self.data_history)}/{len(self.data_history)})")
                else:
                    self.status_bar.config(text="没有历史数据可以前进")
        except Exception as e:
            print(f"Error in forward_view: {str(e)}")
            messagebox.showerror("错误", "前进到下一状态时出错")

    def load_saved_widths(self):
        """加载保存的列宽度设置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.saved_widths = json.load(f)
            else:
                self.saved_widths = {}
        except Exception as e:
            print(f"加载列宽度设置时出错: {str(e)}")
            self.saved_widths = {}

    def save_column_widths(self):
        """保存当前的列宽度设置"""
        try:
            widths = {}
            for col in self.tree['columns']:
                widths[col] = self.tree.column(col, 'width')

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(widths, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"保存列宽度设置时出错: {str(e)}")

    def on_column_configure(self, event):
        """列宽度改变时的处理函数"""
        # 直接保存列宽度，不使用定时器
        self.save_column_widths()

    def create_tooltip(self, widget, text):
        """为控件创建提示工具"""
        def enter(event):
            # 创建提示窗口
            x, y, _, _ = widget.bbox("insert")
            x += widget.winfo_rootx() + 25
            y += widget.winfo_rooty() + 25

            # 创建一个顶级窗口
            self.tooltip = tk.Toplevel(widget)
            self.tooltip.wm_overrideredirect(True)  # 无边框窗口
            self.tooltip.wm_geometry(f"+{x}+{y}")

            # 创建标签显示提示文本
            label = tk.Label(self.tooltip, text=text, justify='left',
                           background="#ffffe0", relief="solid", borderwidth=1,
                           font=("微软雅黑", 9, "normal"), padx=5, pady=5)
            label.pack()

        def leave(event):
            # 销毁提示窗口
            if hasattr(self, 'tooltip'):
                self.tooltip.destroy()

        # 绑定鼠标进入和离开事件
        widget.bind("<Enter>", enter)
        widget.bind("<Leave>", leave)

    def load_main_window_size(self):
        """加载主窗口大小和位置"""
        try:
            if os.path.exists(self.main_window_size_file):
                with open(self.main_window_size_file, 'r', encoding='utf-8') as f:
                    window_size = json.load(f)
                    # 获取屏幕尺寸
                    screen_width = self.root.winfo_screenwidth()
                    screen_height = self.root.winfo_screenheight()

                    # 获取保存的窗口尺寸，如果超出屏幕则使用默认值
                    width = min(window_size.get('width', 1024), screen_width)
                    height = min(window_size.get('height', 768), screen_height)

                    # 获取保存的窗口位置，确保窗口在屏幕内
                    x = max(0, min(window_size.get('x', 0), screen_width - width))
                    y = max(0, min(window_size.get('y', 0), screen_height - height))

                    # 设置窗口大小和位置
                    self.root.geometry(f"{width}x{height}+{x}+{y}")
            else:
                # 如果配置文件不存在，使用默认值并居中显示
                width = 1024
                height = 768
                x = (self.root.winfo_screenwidth() - width) // 2
                y = (self.root.winfo_screenheight() - height) // 2
                self.root.geometry(f"{width}x{height}+{x}+{y}")
        except Exception as e:
            print(f"加载主窗口配置出错: {str(e)}")
            # 使用默认配置
            self.root.geometry("1024x768")

    def save_main_window_size(self):
        """保存主窗口大小和位置"""
        try:
            # 获取当前窗口位置和大小
            geometry = self.root.geometry()
            match = re.match(r'(\d+)x(\d+)\+(-?\d+)\+(-?\d+)', geometry)
            if match:
                width, height, x, y = map(int, match.groups())
                config = {
                    'width': width,
                    'height': height,
                    'x': x,
                    'y': y
                }
                with open(self.main_window_size_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"保存主窗口配置出错: {str(e)}")

    def on_closing(self):
        """窗口关闭时的处理函数"""
        try:
            # 保存列宽度
            self.save_column_widths()
            # 保存窗口大小和位置
            self.save_main_window_size()
        finally:
            self.root.destroy()

    def load_last_directory(self):
        """加载上次导入的目录路径"""
        try:
            if os.path.exists(self.last_dir_file):
                with open(self.last_dir_file, 'r', encoding='utf-8') as f:
                    self.last_directory = json.load(f).get('last_directory', '')
            else:
                self.last_directory = os.path.expanduser("~\\Documents")
        except Exception as e:
            print(f"加载上次目录出错: {str(e)}")
            self.last_directory = os.path.expanduser("~\\Documents")

    def save_last_directory(self, directory):
        """保存最后一次导入的目录路径"""
        try:
            with open(self.last_dir_file, 'w', encoding='utf-8') as f:
                json.dump({'last_directory': directory}, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"保存目录路径时出错: {str(e)}")

    def import_data(self):
        file_types = [
            ('Excel files', '*.xlsx'),
            ('Text files', '*.txt'),
            ('All files', '*.*')
        ]
        file_path = filedialog.askopenfilename(
            filetypes=file_types,
            defaultextension='.xlsx',
            initialdir=self.last_directory
        )
        if not file_path:
            return

        # 保存新的目录路径
        new_directory = os.path.dirname(file_path)
        self.last_directory = new_directory
        self.save_last_directory(new_directory)

        try:
            self.status_bar.config(text="正在导入数据...")

            # 清空现有数据
            for item in self.tree.get_children():
                self.tree.delete(item)

            # 清空原始数据
            self.original_data = []

            if file_path.endswith('.xlsx'):
                try:
                    # 读取Excel文件
                    df = pd.read_excel(file_path, header=None)
                    # 将所有数据转换为字符串
                    df = df.astype(str)
                    # 处理每一行
                    for index, row in df.iterrows():
                        try:
                            # 获取并清理数据
                            row_data = row.tolist()
                            if len(row_data) >= 9:
                                if self.validate_lottery_data(row_data[:9]):
                                    self.analyze_and_insert_row(row_data[:9])
                        except:
                            print(f"跳过无效行: {index + 1}")
                            continue
                except Exception as e:
                    print(f"Excel处理错误: {str(e)}")
                    raise
            else:
                # 尝试不同的编码读取文本文件
                encodings = ['utf-8', 'gb2312', 'gbk']
                content = None

                for encoding in encodings:
                    try:
                        with open(file_path, 'r', encoding=encoding) as f:
                            content = f.read()
                            break
                    except:
                        continue

                if content is None:
                    raise ValueError("无法读取文件，请检查文件编码")

                # 处理文本文件数据
                lines = [line.strip() for line in content.split('\n') if line.strip()]
                for line in lines:
                    try:
                        # 直接按空格分割
                        parts = line.split()
                        if len(parts) >= 9:
                            if self.validate_lottery_data(parts[:9]):
                                self.analyze_and_insert_row(parts[:9])
                    except Exception as e:
                        print(f"Error processing line: {line}")
                        print(f"Error details: {str(e)}")
                        continue

            # 保存出的数据作为原始数据
            self.original_data = []
            for item in self.tree.get_children():
                self.original_data.append({
                    'values': self.tree.item(item)['values'],
                    'tags': self.tree.item(item)['tags']
                })

            record_count = len(self.tree.get_children())
            if record_count > 0:
                self.status_bar.config(text=f"数据导入完成，共{record_count}条记录")
            else:
                raise ValueError("没有成功导入任何数据")

        except Exception as e:
            messagebox.showerror("错误", f"导入数据时出错：{str(e)}")
            self.status_bar.config(text="导入数据失败")
            print(f"Error details: {str(e)}")

    def calculate_missing_numbers(self):
        """计算遗漏值"""
        try:
            # 获取所有数据项
            items = self.tree.get_children()
            if not items:
                messagebox.showinfo("提示", "没有数据可以计算遗漏值")
                return

            # 获取序号列和遗漏列的索引
            序号_index = self.tree['columns'].index('序号')
            遗漏_index = self.tree['columns'].index('遗漏')

            # 遍历所有项计算遗漏值
            for i, item in enumerate(items):
                values = list(self.tree.item(item)['values'])
                if i == 0:
                    # 第一条记录遗漏为0
                    values[遗漏_index] = "0"
                else:
                    # 获取当前和前一条记录的序号
                    current_序号 = int(values[序号_index])
                    prev_values = self.tree.item(items[i-1])['values']
                    prev_序号 = int(prev_values[序号_index])
                    # 计算遗漏值
                    遗漏 = current_序号 - prev_序号 - 1
                    values[遗漏_index] = str(遗漏)

                # 更新树形视图中的值
                self.tree.item(item, values=values)

            self.status_bar.config(text="遗漏值计算完成")

        except Exception as e:
            messagebox.showerror("错误", f"计算遗漏值时出错：{str(e)}")
            self.status_bar.config(text="计算遗漏值失败")

    def show_context_menu(self, event):
        """显示右键菜单"""
        try:
            # 获取点击位置的item
            item = self.tree.identify_row(event.y)
            if item:
                # 如果点击在某一行上
                # 如果该行未被选中，则选中该行
                if item not in self.tree.selection():
                    self.tree.selection_set(item)
                # 显示右键菜单
                self.context_menu.post(event.x_root, event.y_root)
        except Exception as e:
            print(f"显示右键菜单时出错: {str(e)}")

    def copy_selected(self):
        """复制选中的数据到剪贴板"""
        try:
            # 获取选中的行
            selected_items = self.tree.selection()
            if not selected_items:
                messagebox.showwarning("警告", "请先选择要复制的记录")
                return

            # 获取列名
            columns = self.tree['columns']

            # 准备复制的内容
            copy_content = []
            # 添加表头
            copy_content.append('\t'.join(columns))

            # 添加数据行
            for item in selected_items:
                values = self.tree.item(item)['values']
                # 确保所有值都转换为字符串
                values = [str(val) if val is not None else '' for val in values]
                copy_content.append('\t'.join(values))

            # 合并所有内容，使用换行符分隔
            final_content = '\n'.join(copy_content)

            # 复制到剪贴板
            self.root.clipboard_clear()
            self.root.clipboard_append(final_content)

            # 更新状态栏
            self.status_bar.config(text=f"已复制 {len(selected_items)} 条记录到剪贴板")

        except Exception as e:
            messagebox.showerror("错误", f"复制数据时出错：{str(e)}")
            self.status_bar.config(text="复制数据失败")

    def analyze_selected_rows(self):
        """分析选择行功能，只显示选择的行"""
        try:
            # 在分析前保存当前状态
            self.save_current_state()

            # 获取选中的行
            selected_items = self.tree.selection()
            if not selected_items:
                messagebox.showwarning("警告", "请先选择要分析的记录")
                return

            # 获取所有行
            all_items = self.tree.get_children()

            # 隐藏未选中的行
            for item in all_items:
                if item not in selected_items:
                    self.tree.detach(item)  # 隐藏不匹配的行

            # 更新状态栏
            self.status_bar.config(text=f"正在分析 {len(selected_items)} 条选中记录")

        except Exception as e:
            messagebox.showerror("错误", f"分析选择行时出错：{str(e)}")
            self.status_bar.config(text="分析选择行失败")

    def show_help(self):
        """显示使用说明窗口"""
        help_window = tk.Toplevel(self.root)
        help_window.title("使用说明")

        # 设置窗口大小
        window_width = 800
        window_height = 600

        # 获取屏幕尺寸
        screen_width = help_window.winfo_screenwidth()
        screen_height = help_window.winfo_screenheight()

        # 计算窗口位置使其居中
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # 设置窗口大小和位置
        help_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 创建文本框和滚动条
        text_frame = ttk.Frame(help_window)
        text_frame.pack(fill='both', expand=True, padx=5, pady=5)

        help_text = tk.Text(text_frame, wrap=tk.WORD, font=("微软雅黑", 12))
        help_text.pack(fill='both', expand=True, side='left')

        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=help_text.yview)
        scrollbar.pack(side='right', fill='y')
        help_text.configure(yscrollcommand=scrollbar.set)

        # 使用说明内容
        help_content = """中道体彩排列5分析软件使用说明

官方主页：http://cp.wuaze.com
作者：仲道五工作室
微信公众号：仲道五

一、软件简介
本软件根据曾猜中过13次超级大乐透大奖的数学家彩票大师陆金的分析理论创建,
专注分析大小、奇偶组合二维定号法并增加分析期号和篮球的分析功能供大家研究。

本软件特点：
- 所有参数选项都可以自由输入数字。
- 分析的数据是以主窗口中显示的数据为范围，比如下载数据后搜一位为1时，再点分析功能里面的分析，
分析的是一位为1时所有数据，方便彩民在规律中找规律更易中彩。

1.1 数据说明
期尾奇列分析期号十位和个位:
- 0代表偶数
- 1代表奇数

期尾大列分析期号十位和个位:
- 0代表小
- 1代表大

1.2 数据管理
- 数据下载：直接从网络下载最新数据
- 导入数据：支持导入Excel文件(.xlsx)或文本文件(.txt)
- 导出数据：可将当前数据导出为Excel或文本文件
- 删除数据：选中要删除的行，右键选择删除

二、数据管理功能
   2.1 数据下载
   - 直接从网络下载最新开奖数据
   - 自动解析并显示数据
   - 支持断点续传和进度显示

   2.2 导入号码
   - 支持导入Excel文件(.xlsx)和文本文件(.txt)
   - 自动识别文件编码
   - 自动验证数据格式

   2.3 导出号码
   - 支持导出为Excel或文本格式
   - 保存当前显示的分析结果
   - 包含所有统计指标数据

三、分析工具功能
   3.1 遗漏计算
   - 自动计算每期遗漏值
   - 显示在遗漏列中
   - 支持遗漏值排序和筛选

四、搜索功能
   4.1 普通搜索
   - 支持多列条件组合搜索
   - 实时显示搜索结果
   - 可通过返回按钮恢复显示全部数据

五、数据显示功能
   5.1 表格显示
   - 自动调整列宽
   - 支持列宽手动调整
   - 自动保存列宽设置

   5.2 右键菜单
   - 复制选中数据
   - 删除选中记录
   - 显示选中行分析

六、使用技巧
   - 使用右键菜单可以快速操作选中数据
   - 分析工具支持自定义参数，可根据需要调整
   - 搜索结果可以继续进行其他分析
   - 可以结合多个分析工具深入研究数据规律
   - 重要分析结果建议及时导出保存

七、注意事项
   - 首次使用建议先下载或导入最新数据
   - 分析大量数据时可能需要等待
   - 建议定期保存分析结果
   - 可以随时中断长时间的分析过程
   - 使用高级功能时注意参数设置的合理性

本软件会持续更新优化,希望大家多多支持。
"""

        # 插入帮助内容
        help_text.insert('1.0', help_content)
        help_text.config(state='disabled')  # 设置为只读

    def show_about(self):
        """显示关于窗口"""
        about_window = tk.Toplevel(self.root)
        about_window.title("关于")
        about_window.geometry("500x280")  # 增加窗口高度以容纳新链接

        # 设置窗口在屏幕中央
        about_window.update_idletasks()
        width = about_window.winfo_width()
        height = about_window.winfo_height()
        x = (about_window.winfo_screenwidth() // 2) - (width // 2)
        y = (about_window.winfo_screenheight() // 2) - (height // 2)
        about_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

        # 创建内容框架
        content_frame = ttk.Frame(about_window, padding="20")
        content_frame.pack(fill='both', expand=True)

        # 添加软件信息
        ttk.Label(content_frame, text="中道体彩排列5分析软件", font=('SimHei', 14, 'bold')).pack(pady=10)
        ttk.Label(content_frame, text="本软件根据13次猜中超级大乐透大奖的数学家陆金的理论设计").pack(pady=5)
        ttk.Label(content_frame, text="作者: 仲道五工作室    微信公众号：仲道五").pack(pady=5)
        ttk.Label(content_frame, text="© 2024 版权所有").pack(pady=5)
        ttk.Label(content_frame, text="版本: 3.1").pack(pady=5)


        # 添加官方主页链接
        def open_homepage():
            webbrowser.open("http://cp.wuaze.com")

        # 创建一个带下划线的链接样式的标签
        homepage_label = ttk.Label(content_frame, text="官方主页：http://cp.wuaze.com",
                                 font=("微软雅黑", 9, "underline"),
                                 foreground="blue",
                                 cursor="hand2")
        homepage_label.pack(pady=10)
        homepage_label.bind("<Button-1>", lambda e: open_homepage())

    def show_column_width_settings(self):
        """显示列宽度设置对话框"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("列宽设置")
        settings_window.geometry("400x500")
        settings_window.resizable(False, False)
        settings_window.transient(self.root)
        settings_window.grab_set()

        # 创建滚动条和框架
        canvas = tk.Canvas(settings_window)
        scrollbar = ttk.Scrollbar(settings_window, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 放置Canvas和滚动条
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 当前列宽度
        current_widths = {}
        for col in self.tree['columns']:
            current_widths[col] = self.tree.column(col, 'width')

        # 创建输入框和标签
        entries = {}
        row = 0
        for col in self.tree['columns']:
            ttk.Label(scrollable_frame, text=f"{col}:").grid(row=row, column=0, sticky="w", padx=10, pady=5)
            entry = ttk.Entry(scrollable_frame, width=10)
            entry.insert(0, str(current_widths.get(col, 100)))
            entry.grid(row=row, column=1, padx=10, pady=5)
            entries[col] = entry
            row += 1

        # 创建按钮框架
        button_frame = ttk.Frame(settings_window)
        button_frame.pack(side="bottom", pady=10)

        # 保存按钮
        def save_settings():
            try:
                # 保存新的宽度设置
                for col, entry in entries.items():
                    try:
                        width = int(entry.get())
                        if width < 20:  # 设置最小宽度限制
                            width = 20
                        self.tree.column(col, width=width)
                    except ValueError:
                        messagebox.showwarning("警告", f"列 '{col}' 的宽度必须是数字")

                # 保存设置到文件
                self.save_column_widths()
                settings_window.destroy()
                messagebox.showinfo("成功", "列宽设置已保存")
            except Exception as e:
                messagebox.showerror("错误", f"保存设置时出错: {str(e)}")

        # 取消按钮
        def cancel():
            settings_window.destroy()

        ttk.Button(button_frame, text="保存", command=save_settings).pack(side="left", padx=10)
        ttk.Button(button_frame, text="取消", command=cancel).pack(side="left", padx=10)

        # 使对话框居中
        settings_window.update_idletasks()
        width = settings_window.winfo_width()
        height = settings_window.winfo_height()
        x = (settings_window.winfo_screenwidth() // 2) - (width // 2)
        y = (settings_window.winfo_screenheight() // 2) - (height // 2)
        settings_window.geometry(f"{width}x{height}+{x}+{y}")

    def show_size_prediction(self):
        """显示大小预测窗口"""
        prediction_window = tk.Toplevel(self.root)
        prediction_window.title("大小预测分析")

        # 设置窗口大小和位置
        window_width = 800
        window_height = 600
        x = (prediction_window.winfo_screenwidth() - window_width) // 2
        y = (prediction_window.winfo_screenheight() - window_height) // 2
        prediction_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 创建主框架
        main_frame = ttk.Frame(prediction_window, padding="10")
        main_frame.pack(fill='both', expand=True)

        # 创建选择框架
        select_frame = ttk.LabelFrame(main_frame, text="选择参数", padding="5")
        select_frame.pack(fill='x', padx=5, pady=5)

        # 创建期号范围选择区域
        range_frame = ttk.Frame(select_frame)
        range_frame.pack(fill='x', padx=5, pady=5)

        ttk.Label(range_frame, text="分析范围：从最近").pack(side='left', padx=2)
        start_period = ttk.Combobox(range_frame, values=['1', '50', '100', '200', '500'], width=5)
        start_period.set('1')  # 设置默认值
        start_period.pack(side='left', padx=2)

        ttk.Label(range_frame, text="期到最近").pack(side='left', padx=2)
        end_period = ttk.Combobox(range_frame, values=['100', '200', '500', '99999'], width=6)
        end_period.set('200')  # 设置默认值
        end_period.pack(side='left', padx=2)
        ttk.Label(range_frame, text="期").pack(side='left', padx=2)

        # 创建号码位数选择区域
        number_frame = ttk.Frame(select_frame)
        number_frame.pack(fill='x', padx=5, pady=5)

        ttk.Label(number_frame, text="选择位数:").pack(side='left', padx=5)
        position_var = ttk.Combobox(number_frame, values=['一位', '二位', '三位', '四位', '五位'], width=5)
        position_var.set('二位')  # 默认选择二位
        position_var.pack(side='left', padx=5)

        # 创建号码选择区域
        number_select_frame = ttk.Frame(select_frame)
        number_select_frame.pack(fill='x', padx=5, pady=5)

        ttk.Label(number_select_frame, text="选择号码:").pack(side='left', padx=5)

        # 创建数字选择变量和复选框
        number_vars = {}
        number_checkboxes = ttk.Frame(number_select_frame)
        number_checkboxes.pack(side='left')

        for i in range(10):
            number_vars[i] = tk.BooleanVar(value=False)
            ttk.Checkbutton(number_checkboxes, text=str(i),
                           variable=number_vars[i]).pack(side='left', padx=2)

        # 创建结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="分析结果", padding="5")
        result_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # 创建文本框和滚动条
        text_frame = ttk.Frame(result_frame)
        text_frame.pack(fill='both', expand=True)

        result_text = tk.Text(text_frame, wrap=tk.WORD, width=80, height=20)
        result_text.pack(side='left', fill='both', expand=True)

        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=result_text.yview)
        scrollbar.pack(side='right', fill='y')
        result_text.configure(yscrollcommand=scrollbar.set)

        # 创建进度条
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill='x', padx=5, pady=5)

        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(progress_frame, variable=progress_var, maximum=100)
        progress_bar.pack(fill='x', side='left', expand=True, padx=(0, 5))

        # 创建停止分析的标志
        stop_analysis = tk.BooleanVar(value=False)

        # 记录当前运行的分析类型
        current_analysis = tk.StringVar(value="none")  # 可能的值: "none", "single", "traverse"

        # 创建按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x', padx=5, pady=5)

        def start_analysis():
            """开始分析特定号码的大小预测"""
            # 获取选中的数字
            selected_numbers = [i for i, var in number_vars.items() if var.get()]
            if not selected_numbers:
                messagebox.showwarning("警告", "请至少选择一个号码")
                return

            # 获取期号范围
            try:
                start = int(start_period.get())
                end = int(end_period.get())
                if start >= end:
                    messagebox.showwarning("警告", "起始期号必须小于结束期号")
                    return
            except ValueError:
                messagebox.showwarning("警告", "请输入有效的期号范围")
                return

            # 获取选择的位数
            position = position_var.get()
            position_map = {'一位': 2, '二位': 3, '三位': 4, '四位': 5, '五位': 6}
            selected_position = position_map.get(position)

            # 设置当前分析类型
            current_analysis.set("single")

            # 禁用开始按钮，启用停止按钮
            start_button.configure(state='disabled')
            traverse_button.configure(state='disabled')
            stop_button.configure(state='normal')

            # 清空结果显示
            result_text.delete('1.0', tk.END)

            # 重置停止标志
            stop_analysis.set(False)

            # 获取所有数据
            all_items = self.tree.get_children()
            if not all_items:
                messagebox.showwarning("警告", "没有可分析的数据")
                return

            try:
                # 根据选择的期号范围筛选数据
                total_periods = len(all_items)
                start_index = max(0, total_periods - end)
                end_index = total_periods - start
                analysis_items = all_items[start_index:end_index]
                if len(analysis_items) < 6:
                    messagebox.showwarning("警告", "选择的期号范围内数据不足，无法分析")
                    return
            except Exception as e:
                messagebox.showerror("错误", f"处理数据时出错：{str(e)}")
                return

            try:
                # 开始分析
                total_combinations = len(selected_numbers)
                for index, target_number in enumerate(selected_numbers):
                    if stop_analysis.get():
                        break

                    # 更新进度
                    progress = (index + 1) / total_combinations * 100
                    progress_var.set(progress)
                    prediction_window.update()

                    # 分析每个选中的号码
                    result_text.insert(tk.END, f"\n{position}:\n")
                    result_text.insert(tk.END, f"  当前数字：{target_number}\n")

                    # 遍历数据
                    for i, item in enumerate(analysis_items[:-1]):  # 排除最后一期
                        if stop_analysis.get():
                            break

                        values = self.tree.item(item)['values']
                        if str(values[selected_position]) == str(target_number):  # 检查选中位置是否匹配
                            # 获取相关期号的数据
                            try:
                                up_2_period = self.tree.item(analysis_items[i-2])['values']
                                up_1_period = self.tree.item(analysis_items[i-1])['values']
                                current_period = values
                                next_period = self.tree.item(analysis_items[i+1])['values']

                                # 计算组合值
                                combination_sum = (
                                    int(up_2_period[2]) +  # 上2期一位
                                    int(up_2_period[5]) +  # 上2期四位
                                    int(up_1_period[4])    # 上1期三位
                                )
                                tail_value = combination_sum % 10

                                # 确定预测范围
                                predicted_range = [5,6,7,8,9] if tail_value >= 5 else [0,1,2,3,4]

                                # 检查预测是否准确
                                next_value = int(next_period[selected_position])
                                is_correct = (next_value in predicted_range)

                                # 显示结果
                                result_text.insert(tk.END, f"  期号：{current_period[0]}\n")
                                result_text.insert(tk.END, f"  实际下期数字：{next_value} {'√' if is_correct else '×'}\n")
                                result_text.insert(tk.END, f"  使用组合：[上2期一位{up_2_period[2]}+上2期四位{up_2_period[5]}+上1期三位{up_1_period[4]}={combination_sum}]\n")
                                result_text.insert(tk.END, f"  预测范围：{predicted_range}\n\n")

                                # 自动滚动到底部
                                result_text.see(tk.END)
                                prediction_window.update()

                            except IndexError:
                                continue

                # 分析完成后恢复按钮状态
                start_button.configure(state='normal')
                traverse_button.configure(state='normal')
                stop_button.configure(state='disabled')
                progress_var.set(100)

                # 更新状态
                current_analysis.set("none")

                # 在结果文本框中显示分析完成信息，而不是弹窗
                if not stop_analysis.get():
                    result_text.insert(tk.END, "\n分析完成！\n")
                    result_text.see(tk.END)

            except Exception as e:
                messagebox.showerror("错误", f"分析过程中出错：{str(e)}")
                start_button.configure(state='normal')
                traverse_button.configure(state='normal')
                stop_button.configure(state='disabled')
                current_analysis.set("none")

        def stop_analysis_func():
            """停止当前分析"""
            stop_analysis.set(True)

            # 根据当前分析类型恢复按钮状态
            if current_analysis.get() in ["single", "traverse", "database", "verify"]:
                start_button.configure(state='normal')
                traverse_button.configure(state='normal')
                database_button.configure(state='normal')
                verify_button.configure(state='normal')

            stop_button.configure(state='disabled')
            current_analysis.set("none")

            # 在结果文本框中显示分析已停止信息
            result_text.insert(tk.END, "\n分析已停止\n")
            result_text.see(tk.END)

        def analyze_combinations():
            """遍历前5期所有位次的2~4元素组合，统计每组组合在指定位置出现指定号码时，用这些跨期跨位组合预测下期该位号码大小的准确率，输出最佳组合。"""
            from itertools import combinations as iter_combinations
            # 获取期号范围
            try:
                start = int(start_period.get())
                end = int(end_period.get())
                if start >= end:
                    messagebox.showwarning("警告", "起始期号必须小于结束期号")
                    return
            except ValueError:
                messagebox.showwarning("警告", "请输入有效的期号范围")
                return

            # 获取选择的位数和号码
            position = position_var.get()
            position_map = {'一位': 2, '二位': 3, '三位': 4, '四位': 5, '五位': 6}
            selected_position = position_map.get(position)
            selected_numbers = [i for i, var in number_vars.items() if var.get()]
            if not selected_numbers:
                messagebox.showwarning("警告", "请至少选择一个号码")
                return
            if len(selected_numbers) > 1:
                messagebox.showwarning("警告", "遍历分析只支持选择一个号码")
                return
            target_number = selected_numbers[0]

            # 设置当前分析类型
            current_analysis.set("traverse")
            # 清空结果显示
            result_text.delete('1.0', tk.END)
            result_text.insert(tk.END, f"遍历前5期所有位次2~4元素组合，统计预测{position}为{target_number}时的最佳组合准确率...\n\n")
            result_text.see(tk.END)

            # 获取所有数据
            all_items = self.tree.get_children()
            if not all_items:
                messagebox.showwarning("警告", "没有可分析的数据")
                return

            try:
                # 根据选择的期号范围筛选数据
                # 注意：这里需要考虑索引和"最近N期"的定义。
                # "最近1期"是all_items[-1]
                # "最近m期"是all_items[-m]
                # "从最近n期到最近m期"对应all_items[len(all_items)-m : len(all_items)-n+1]
                total_all_items = len(all_items)
                if total_all_items < end:
                     messagebox.showwarning("警告", f"总数据量不足{end}期，无法分析最近{start}到{end}期")
                     return

                # 确定分析数据的实际切片范围
                # 例如：最近1期到最近200期 -> all_items[-200 : -1+1] -> all_items[-200:]
                # 例如：最近50期到最近200期 -> all_items[-200 : -50+1]
                # 如果start=1, 则切片到最后
                end_slice_index = total_all_items - (start - 1) if start > 1 else total_all_items
                start_slice_index = total_all_items - end

                # 确保切片索引有效
                if start_slice_index < 0:
                     start_slice_index = 0
                if end_slice_index > total_all_items:
                     end_slice_index = total_all_items

                analysis_items = all_items[start_slice_index:end_slice_index]

                if len(analysis_items) < 6: # 至少需要当前期+下一期+前5期 = 7期，但只需要前5期+当前期+下一期，所以是5+1+1=7期，如果只统计不预测下一期，只需要6期
                     messagebox.showwarning("警告", f"选择的期号范围内数据不足（至少需要6期），无法分析。当前范围包含{len(analysis_items)}期。")
                     return

                # 找到指定位置等于指定号码的所有期号索引 WITHIN analysis_items
                # 这些期号必须有前5期和后1期，才能用于统计准确率
                target_indices_in_analysis = []
                # 需要保证当前期 ti >= 5 (有前5期) 且 ti < len(analysis_items) - 1 (有后1期)
                for i in range(len(analysis_items) - 1): # 遍历到倒数第二期，确保有下一期
                    if i < 5: continue # 确保有前5期
                    values = self.tree.item(analysis_items[i])['values']
                    if str(values[selected_position]) == str(target_number):
                        target_indices_in_analysis.append(i)

                if not target_indices_in_analysis:
                    result_text.insert(tk.END, f"在所选范围（最近{start}到{end}期）内未找到{position}为{target_number}且前后数据充足的期号。\\n")
                    return

                result_text.insert(tk.END, f"在所选范围（最近{start}到{end}期）内找到{len(target_indices_in_analysis)}个符合条件的期号进行分析。\\n\\n")
                result_text.see(tk.END)


                # 生成前5期所有位次的组合（25个元素）
                all_pos = []
                # 期偏移从1到5表示"上1期"到"上5期"
                # 位索引从2到6表示"一位"到"五位"
                for period_offset in range(1, 6):
                    for pos in range(2, 7):
                        # 存储为 (期偏移, 对应的Treeview列索引)
                        all_pos.append((period_offset, pos))

                # 枚举2~4个元素的所有组合 (每个元素是 (期偏移, 位索引))
                combos = []
                for r in range(2, 5): # 2到4个位置
                    combos.extend(iter_combinations(all_pos, r))
                total_combos = len(combos)

                combo_results = []
                for idx, combo in enumerate(combos):
                    if stop_analysis.get():
                        break
                    correct = 0
                    total = 0

                    # 对每个符合条件的期号进行预测和统计
                    for ti_in_analysis in target_indices_in_analysis:
                        # ti_in_analysis 是该期在 analysis_items 中的索引
                        # 需要确保 ti_in_analysis >= 5 (有前5期)
                        # 且 ti_in_analysis + 1 < len(analysis_items) (有下一期)
                        # 这已经在 target_indices_in_analysis 的生成时保证了

                        # 计算组合值
                        sum_val = 0
                        valid_combo_instance = True # 标记当前组合在当前期是否能取到所有数据
                        # combo 包含多个 (期偏移, 位索引)
                        for period_offset, pos in combo:
                             source_idx_in_analysis = ti_in_analysis - period_offset
                             # 理论上生成 target_indices_in_analysis 已经确保 ti_in_analysis >= 5
                             # 但为保险起见，再次检查 source_idx_in_analysis >= 0
                             if source_idx_in_analysis < 0:
                                  valid_combo_instance = False
                                  break
                             try:
                                  source_values = self.tree.item(analysis_items[source_idx_in_analysis])['values']
                                  sum_val += int(source_values[pos])
                             except (ValueError, IndexError): # 数据格式错误或索引越界（不应发生）
                                 valid_combo_instance = False
                                 break

                        if not valid_combo_instance:
                            continue # 跳过当前组合在当前期号上的预测统计

                        # 进行预测
                        tail = sum_val % 10
                        predicted_range = [5,6,7,8,9] if tail >= 5 else [0,1,2,3,4]

                        # 获取下一期实际值
                        try:
                             next_values = self.tree.item(analysis_items[ti_in_analysis + 1])['values']
                             next_actual_value = int(next_values[selected_position])
                             # 检查预测是否准确
                             if next_actual_value in predicted_range:
                                 correct += 1
                             total += 1
                        except (ValueError, IndexError): # 下一期数据无效或不存在（不应发生，但为鲁棒性考虑）
                             continue # 跳过当前预测统计


                    accuracy = (correct / total * 100) if total > 0 else 0
                    # 组合描述
                    combo_desc = '+'.join([f"上{period_offset}期{['一','二','三','四','五'][pos-2]}" for period_offset, pos in combo])
                    combo_results.append({
                        'combination': combo,
                        'description': combo_desc,
                        'accuracy': accuracy,
                        'correct': correct,
                        'total': total
                    })
                    # 更新进度条
                    progress = (idx + 1) / total_combos * 100
                    progress_var.set(progress)
                    prediction_window.update()

                # 按准确率排序
                combo_results.sort(key=lambda x: x['accuracy'], reverse=True)

                # 显示结果
                result_text.insert(tk.END, f"共分析{len(combo_results)}组组合，结果如下（按准确率降序）：\n\n")
                for i, result in enumerate(combo_results, 1):
                    result_text.insert(tk.END, f"{i}. 组合：{result['description']}\n")
                    result_text.insert(tk.END, f"   准确率：{result['accuracy']:.2f}% ({result['correct']}/{result['total']})\n\n")

                # 显示最佳组合在最近一次出现指定号码时的预测结果
                if combo_results and target_indices_in_analysis:
                    best_combo_result = combo_results[0]
                    best_combo = best_combo_result['combination']

                    # 找到最近一次出现指定号码的期号索引
                    latest_target_index_in_analysis = target_indices_in_analysis[-1] # 在analysis_items中的索引
                    latest_target_item = analysis_items[latest_target_index_in_analysis] # 最近一次出现指定号码的item
                    latest_target_values = self.tree.item(latest_target_item)['values'] # 对应的值

                    # 获取预测下一期的实际数据（如果存在）
                    next_period_item = None
                    next_period_values = None
                    next_actual_value = "未知"
                    is_prediction_correct = "-"
                    try:
                        # 下一期在all_items中的索引
                        next_item_index_in_all = all_items.index(latest_target_item) + 1
                        if next_item_index_in_all < len(all_items):
                             next_period_item = all_items[next_item_index_in_all]
                             next_period_values = self.tree.item(next_period_item)['values']
                             next_actual_value = int(next_period_values[selected_position])
                    except ValueError: # 如果latest_target_item不在all_items中（不应该发生）
                        pass
                    except IndexError: # 如果是all_items的最后一期
                        pass

                    # 使用最佳组合预测下一期大小
                    sum_val = 0
                    valid_prediction = True
                    prediction_source_values = {}
                    for period_offset, pos in best_combo:
                        idx_in_analysis = latest_target_index_in_analysis - period_offset
                        if idx_in_analysis < 0 or idx_in_analysis >= len(analysis_items):
                            valid_prediction = False
                            break
                        source_item = analysis_items[idx_in_analysis]
                        source_values = self.tree.item(source_item)['values']
                        value = int(source_values[pos])
                        sum_val += value
                        period_desc = f"上{period_offset}期"
                        pos_desc = ['一','二','三','四','五'][pos-2]
                        prediction_source_values[f"{period_desc}{pos_desc}"] = value

                    if valid_prediction:
                        tail = sum_val % 10
                        predicted_range = [5,6,7,8,9] if tail >= 5 else [0,1,2,3,4]
                        predicted_size = '大' if tail >= 5 else '小'

                        if next_period_values:
                            is_prediction_correct = '√' if next_actual_value in predicted_range else '×'

                        result_text.insert(tk.END, f"\n-- 最佳组合在最近一次出现{position}为{target_number}时的预测结果 --\n")
                        result_text.insert(tk.END, f"期号：{latest_target_values[0]} (出现{position}为{target_number})\n")
                        result_text.insert(tk.END, f"  当前数字 ({position})：{target_number}\n")
                        if next_period_values:
                            result_text.insert(tk.END, f"  实际下期数字 ({position})：{next_actual_value} {is_prediction_correct}\n")
                        else:
                             result_text.insert(tk.END, f"  实际下期数字 ({position})：未知\n")
                        result_text.insert(tk.END, f"  使用组合：{best_combo_result['description']} = ")
                        # 显示组合的具体数值
                        combo_value_strings = [f"{desc}{val}" for desc, val in prediction_source_values.items()]
                        result_text.insert(tk.END, '+'.join(combo_value_strings) + f" = {sum_val} (尾{tail})\n")

                        result_text.insert(tk.END, f"  预测范围：{predicted_range} ({predicted_size})\n")

                    result_text.see(tk.END)

                else:
                    if combo_results:
                         result_text.insert(tk.END, f"\n未找到符合条件的期号来显示最佳组合的预测结果。\n")
                    elif target_indices_in_analysis:
                         result_text.insert(tk.END, f"\n未找到有效的组合进行预测。\n")

                result_text.insert(tk.END, "\n组合分析完成！\n")
                result_text.see(tk.END)
                progress_var.set(100)
            except Exception as e:
                messagebox.showerror("错误", f"分析过程中出错：{str(e)}")
            finally:
                start_button.configure(state='normal')
                traverse_button.configure(state='normal')
                stop_button.configure(state='disabled')
                current_analysis.set("none")

        def start_traverse():
            """开始遍历分析"""
            traverse_button.configure(state='disabled')
            start_button.configure(state='disabled')
            stop_button.configure(state='normal')
            stop_analysis.set(False)
            analyze_combinations()

        def create_prediction_database():
            """创建预测数据库，记录每位0-9的最佳预测组合"""
            import sqlite3
            from itertools import combinations as iter_combinations

            # 设置当前分析类型
            current_analysis.set("database")

            # 禁用所有按钮，启用停止按钮
            start_button.configure(state='disabled')
            traverse_button.configure(state='disabled')
            database_button.configure(state='disabled')
            verify_button.configure(state='disabled')
            stop_button.configure(state='normal')

            # 重置停止标志
            stop_analysis.set(False)

            try:
                # 获取期号范围
                start = int(start_period.get())
                end = int(end_period.get())
                if start >= end:
                    messagebox.showwarning("警告", "起始期号必须小于结束期号")
                    return

                # 清空结果显示
                result_text.delete('1.0', tk.END)
                result_text.insert(tk.END, "开始创建预测数据库...\n")
                result_text.insert(tk.END, f"分析范围：最近{start}期到最近{end}期的数据\n\n")
                result_text.see(tk.END)

                # 获取所有数据
                all_items = self.tree.get_children()
                if not all_items:
                    messagebox.showwarning("警告", "没有可分析的数据")
                    return

                # 创建或连接数据库
                conn = sqlite3.connect('plwdx.db')  # 使用SQLite而不是Access
                cursor = conn.cursor()

                # 创建表（如果不存在）
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS prediction_combinations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        position TEXT NOT NULL,
                        number INTEGER NOT NULL,
                        combination_desc TEXT NOT NULL,
                        combination_data TEXT NOT NULL,
                        accuracy REAL NOT NULL,
                        correct_count INTEGER NOT NULL,
                        total_count INTEGER NOT NULL,
                        created_date TEXT NOT NULL,
                        UNIQUE(position, number)
                    )
                ''')

                # 检查已存在的记录
                cursor.execute('SELECT position, number FROM prediction_combinations')
                existing_records = set(cursor.fetchall())

                # 需要分析的位置和数字组合
                positions = ['一位', '二位', '三位', '四位', '五位']
                numbers = list(range(10))

                total_tasks = 0
                completed_tasks = 0

                # 计算总任务数（只计算未记录的）
                for position in positions:
                    for number in numbers:
                        if (position, number) not in existing_records:
                            total_tasks += 1

                # 根据选择的期号范围筛选数据
                total_all_items = len(all_items)
                if total_all_items < end:
                    messagebox.showwarning("警告", f"总数据量不足{end}期，无法分析")
                    return

                end_slice_index = total_all_items - (start - 1) if start > 1 else total_all_items
                start_slice_index = total_all_items - end

                if start_slice_index < 0:
                    start_slice_index = 0
                if end_slice_index > total_all_items:
                    end_slice_index = total_all_items

                analysis_items = all_items[start_slice_index:end_slice_index]

                if len(analysis_items) < 6:
                    messagebox.showwarning("警告", "选择的期号范围内数据不足，无法分析")
                    return

                if total_tasks == 0:
                    result_text.insert(tk.END, "数据库已包含所有位置和数字的最佳组合记录。\n")
                    result_text.see(tk.END)
                    return

                result_text.insert(tk.END, f"需要分析 {total_tasks} 个位置-数字组合...\n")
                result_text.insert(tk.END, f"基于最近{start}期到最近{end}期共{len(analysis_items)}期的数据进行分析\n\n")
                result_text.see(tk.END)

                # 生成前5期所有位次的组合（25个元素）
                all_pos = []
                for period_offset in range(1, 6):
                    for pos in range(2, 7):
                        all_pos.append((period_offset, pos))

                # 枚举2~4个元素的所有组合
                combos = []
                for r in range(2, 5):
                    combos.extend(iter_combinations(all_pos, r))

                # 分析每个位置和数字组合
                for position in positions:
                    if stop_analysis.get():
                        break

                    position_map = {'一位': 2, '二位': 3, '三位': 4, '四位': 5, '五位': 6}
                    selected_position = position_map.get(position)

                    for target_number in numbers:
                        if stop_analysis.get():
                            break

                        # 检查是否已存在记录
                        if (position, target_number) in existing_records:
                            continue

                        result_text.insert(tk.END, f"正在分析 {position} 数字 {target_number}（基于指定期数范围）...\n")
                        result_text.see(tk.END)
                        prediction_window.update()

                        # 找到指定位置等于指定号码的所有期号索引
                        target_indices_in_analysis = []
                        for i in range(len(analysis_items) - 1):
                            if i < 5:
                                continue
                            values = self.tree.item(analysis_items[i])['values']
                            if str(values[selected_position]) == str(target_number):
                                target_indices_in_analysis.append(i)

                        if not target_indices_in_analysis:
                            result_text.insert(tk.END, f"  在指定期数范围内未找到符合条件的数据\n")
                            completed_tasks += 1
                            continue

                        # 分析所有组合，找到准确率最高的
                        best_combo_result = None
                        best_accuracy = -1

                        for combo in combos:
                            if stop_analysis.get():
                                break

                            correct = 0
                            total = 0

                            # 对每个符合条件的期号进行预测和统计
                            for ti_in_analysis in target_indices_in_analysis:
                                sum_val = 0
                                valid_combo_instance = True

                                for period_offset, pos in combo:
                                    source_idx_in_analysis = ti_in_analysis - period_offset
                                    if source_idx_in_analysis < 0:
                                        valid_combo_instance = False
                                        break
                                    try:
                                        source_values = self.tree.item(analysis_items[source_idx_in_analysis])['values']
                                        sum_val += int(source_values[pos])
                                    except (ValueError, IndexError):
                                        valid_combo_instance = False
                                        break

                                if not valid_combo_instance:
                                    continue

                                # 进行预测
                                tail = sum_val % 10
                                predicted_range = [5,6,7,8,9] if tail >= 5 else [0,1,2,3,4]

                                # 获取下一期实际值
                                try:
                                    next_values = self.tree.item(analysis_items[ti_in_analysis + 1])['values']
                                    next_actual_value = int(next_values[selected_position])
                                    if next_actual_value in predicted_range:
                                        correct += 1
                                    total += 1
                                except (ValueError, IndexError):
                                    continue

                            if total > 0:
                                accuracy = (correct / total * 100)
                                if accuracy > best_accuracy:
                                    best_accuracy = accuracy
                                    combo_desc = '+'.join([f"上{period_offset}期{['一','二','三','四','五'][pos-2]}" for period_offset, pos in combo])
                                    best_combo_result = {
                                        'combination': combo,
                                        'description': combo_desc,
                                        'accuracy': accuracy,
                                        'correct': correct,
                                        'total': total
                                    }

                        # 保存最佳组合到数据库
                        if best_combo_result:
                            import json
                            from datetime import datetime

                            cursor.execute('''
                                INSERT OR REPLACE INTO prediction_combinations
                                (position, number, combination_desc, combination_data, accuracy, correct_count, total_count, created_date)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                position,
                                target_number,
                                best_combo_result['description'],
                                json.dumps(best_combo_result['combination']),
                                best_combo_result['accuracy'],
                                best_combo_result['correct'],
                                best_combo_result['total'],
                                datetime.now().isoformat()
                            ))

                            result_text.insert(tk.END, f"  最佳组合: {best_combo_result['description']}\n")
                            result_text.insert(tk.END, f"  准确率: {best_combo_result['accuracy']:.2f}% ({best_combo_result['correct']}/{best_combo_result['total']})（基于指定期数）\n\n")
                        else:
                            result_text.insert(tk.END, f"  在指定期数范围内未找到有效的预测组合\n\n")

                        completed_tasks += 1
                        progress = (completed_tasks / total_tasks) * 100
                        progress_var.set(progress)
                        result_text.see(tk.END)
                        prediction_window.update()

                # 提交事务
                conn.commit()
                conn.close()

                if not stop_analysis.get():
                    result_text.insert(tk.END, f"\n预测数据库创建完成！\n")
                    result_text.insert(tk.END, f"已基于最近{start}期到最近{end}期的数据创建最佳预测组合\n")
                    result_text.see(tk.END)
                    progress_var.set(100)

            except Exception as e:
                result_text.insert(tk.END, f"\n创建数据库时出错：{str(e)}\n")
                messagebox.showerror("错误", f"创建数据库时出错：{str(e)}")
            finally:
                # 恢复按钮状态
                start_button.configure(state='normal')
                traverse_button.configure(state='normal')
                database_button.configure(state='normal')
                verify_button.configure(state='normal')
                stop_button.configure(state='disabled')
                current_analysis.set("none")

        def verify_combinations():
            """检验组合功能，通过预测数据库计算显示每期数据组合预测的成功率"""
            import sqlite3
            import json

            # 设置当前分析类型
            current_analysis.set("verify")

            # 禁用所有按钮，启用停止按钮
            start_button.configure(state='disabled')
            traverse_button.configure(state='disabled')
            database_button.configure(state='disabled')
            verify_button.configure(state='disabled')
            stop_button.configure(state='normal')

            # 重置停止标志
            stop_analysis.set(False)

            try:
                # 获取期号范围
                start = int(start_period.get())
                end = int(end_period.get())

                # 清空结果显示
                result_text.delete('1.0', tk.END)
                result_text.insert(tk.END, "开始检验组合预测成功率...\n")
                result_text.insert(tk.END, f"验证范围：最近{start}期到最近{end}期的数据\n\n")
                result_text.see(tk.END)

                # 连接数据库
                conn = sqlite3.connect('plwdx.db')
                cursor = conn.cursor()

                # 检查数据库是否存在数据
                cursor.execute('SELECT COUNT(*) FROM prediction_combinations')
                record_count = cursor.fetchone()[0]

                if record_count == 0:
                    result_text.insert(tk.END, "预测数据库为空，请先创建预测数据库。\n")
                    return

                # 获取所有预测组合
                cursor.execute('SELECT position, number, combination_desc, combination_data, accuracy FROM prediction_combinations')
                prediction_records = cursor.fetchall()

                # 获取所有数据
                all_items = self.tree.get_children()
                if not all_items:
                    messagebox.showwarning("警告", "没有可分析的数据")
                    return

                # 根据选择的期号范围筛选数据
                total_all_items = len(all_items)
                if total_all_items < end:
                    messagebox.showwarning("警告", f"总数据量不足{end}期，无法分析")
                    return

                end_slice_index = total_all_items - (start - 1) if start > 1 else total_all_items
                start_slice_index = total_all_items - end

                if start_slice_index < 0:
                    start_slice_index = 0
                if end_slice_index > total_all_items:
                    end_slice_index = total_all_items

                analysis_items = all_items[start_slice_index:end_slice_index]

                if len(analysis_items) < 6:
                    messagebox.showwarning("警告", "选择的期号范围内数据不足，无法分析")
                    return

                # 显示数据库和验证信息
                result_text.insert(tk.END, f"数据库中共有 {record_count} 条预测组合记录\n")
                result_text.insert(tk.END, f"将基于最近{start}期到最近{end}期共{len(analysis_items)}期的数据进行验证\n\n")
                result_text.see(tk.END)

                # 统计变量
                total_predictions = 0
                correct_predictions = 0
                position_stats = {}

                # 遍历每期数据进行预测验证
                for i in range(5, len(analysis_items) - 1):  # 确保有前5期和下一期
                    if stop_analysis.get():
                        break

                    current_item = analysis_items[i]
                    current_values = self.tree.item(current_item)['values']
                    next_item = analysis_items[i + 1]
                    next_values = self.tree.item(next_item)['values']

                    period_predictions = 0
                    period_correct = 0

                    # 对每个位置进行预测
                    position_map = {'一位': 2, '二位': 3, '三位': 4, '四位': 5, '五位': 6}

                    for position, col_idx in position_map.items():
                        if stop_analysis.get():
                            break

                        current_number = int(current_values[col_idx])
                        next_actual = int(next_values[col_idx])

                        # 查找对应的预测组合
                        matching_record = None
                        for record in prediction_records:
                            if record[0] == position and record[1] == current_number:
                                matching_record = record
                                break

                        if matching_record:
                            # 解析组合数据
                            combination_data = json.loads(matching_record[3])

                            # 计算组合值
                            sum_val = 0
                            valid_prediction = True

                            for period_offset, pos in combination_data:
                                source_idx = i - period_offset
                                if source_idx < 0:
                                    valid_prediction = False
                                    break
                                try:
                                    source_values = self.tree.item(analysis_items[source_idx])['values']
                                    sum_val += int(source_values[pos])
                                except (ValueError, IndexError):
                                    valid_prediction = False
                                    break

                            if valid_prediction:
                                # 进行预测
                                tail = sum_val % 10
                                predicted_range = [5,6,7,8,9] if tail >= 5 else [0,1,2,3,4]

                                # 检查预测是否正确
                                is_correct = next_actual in predicted_range

                                total_predictions += 1
                                period_predictions += 1

                                if is_correct:
                                    correct_predictions += 1
                                    period_correct += 1

                                # 统计各位置的成功率
                                if position not in position_stats:
                                    position_stats[position] = {'total': 0, 'correct': 0}
                                position_stats[position]['total'] += 1
                                if is_correct:
                                    position_stats[position]['correct'] += 1

                    # 更新进度
                    progress = ((i - 4) / (len(analysis_items) - 6)) * 100
                    progress_var.set(progress)

                    # 显示当期预测结果
                    if period_predictions > 0:
                        period_accuracy = (period_correct / period_predictions) * 100
                        result_text.insert(tk.END, f"期号 {current_values[0]}: {period_correct}/{period_predictions} 正确 ({period_accuracy:.1f}%)\n")
                        result_text.see(tk.END)
                        prediction_window.update()

                # 显示总体统计结果
                if total_predictions > 0:
                    overall_accuracy = (correct_predictions / total_predictions) * 100
                    result_text.insert(tk.END, f"\n=== 检验结果统计 ===\n")
                    result_text.insert(tk.END, f"总预测次数: {total_predictions}\n")
                    result_text.insert(tk.END, f"正确预测次数: {correct_predictions}\n")
                    result_text.insert(tk.END, f"总体成功率: {overall_accuracy:.2f}%\n\n")

                    result_text.insert(tk.END, "各位置成功率:\n")
                    for position, stats in position_stats.items():
                        if stats['total'] > 0:
                            pos_accuracy = (stats['correct'] / stats['total']) * 100
                            result_text.insert(tk.END, f"  {position}: {stats['correct']}/{stats['total']} ({pos_accuracy:.2f}%)\n")
                else:
                    result_text.insert(tk.END, "没有找到可验证的预测数据。\n")

                conn.close()

                if not stop_analysis.get():
                    result_text.insert(tk.END, "\n组合检验完成！\n")
                    result_text.see(tk.END)
                    progress_var.set(100)

            except Exception as e:
                result_text.insert(tk.END, f"\n检验组合时出错：{str(e)}\n")
                messagebox.showerror("错误", f"检验组合时出错：{str(e)}")
            finally:
                # 恢复按钮状态
                start_button.configure(state='normal')
                traverse_button.configure(state='normal')
                database_button.configure(state='normal')
                verify_button.configure(state='normal')
                stop_button.configure(state='disabled')
                current_analysis.set("none")

        # 创建遍历按钮
        traverse_button = ttk.Button(button_frame, text="遍历分析", command=start_traverse)
        traverse_button.pack(side='left', padx=5)

        # 创建开始分析按钮
        start_button = ttk.Button(button_frame, text="开始分析", command=start_analysis)
        start_button.pack(side='left', padx=5)

        # 创建预测数据库按钮
        database_button = ttk.Button(button_frame, text="预测数据库", command=create_prediction_database)
        database_button.pack(side='left', padx=5)

        # 创建检验组合按钮
        verify_button = ttk.Button(button_frame, text="检验组合", command=verify_combinations)
        verify_button.pack(side='left', padx=5)

        # 创建停止按钮（共用）
        stop_button = ttk.Button(button_frame, text="停止分析", command=stop_analysis_func, state='disabled')
        stop_button.pack(side='left', padx=5)

        # 创建关闭按钮
        ttk.Button(button_frame, text="关闭", command=prediction_window.destroy).pack(side='right', padx=5)

if __name__ == "__main__":
    # 预先导入基础模块
    import tkinter as tk
    from tkinter import ttk
    from ttkthemes import ThemedTk
    from PIL import Image, ImageTk
    import time
    import json

    # 创建主窗口
    root = ThemedTk(theme="vista")
    root.withdraw()  # 先隐藏主窗口

    # 创建启动窗口
    splash_root = tk.Toplevel(root)
    splash_root.overrideredirect(True)  # 移除窗口边框

    # 获取屏幕尺寸
    screen_width = splash_root.winfo_screenwidth()
    screen_height = splash_root.winfo_screenheight()

    try:
        # 加载启动图片
        launch_image = Image.open("launch.jpg")
        # 设置启动窗口大小为图片大小
        window_width = launch_image.width
        window_height = launch_image.height

        # 计算窗口位置使其居中
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # 设置窗口位置和大小
        splash_root.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 创建PhotoImage对象
        photo = ImageTk.PhotoImage(launch_image)

        # 创建Label显示图片
        image_label = tk.Label(splash_root, image=photo)
        image_label.pack()

    except Exception as e:
        print(f"加载启动图片失败: {str(e)}")
        # 如果无法加载图片，使用默认尺寸
        window_width = 600
        window_height = 400
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        splash_root.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 创建默认背景
        splash_root.configure(bg='#f0f0f0')
        title_label = tk.Label(splash_root, text="中道体彩排列5分析软件",
                             font=("微软雅黑", 20, "bold"),
                             bg='#f0f0f0')
        title_label.pack(pady=50)

    # 创建进度条框架
    progress_frame = tk.Frame(splash_root, bg='white')
    progress_frame.pack(side='bottom', fill='x', padx=20, pady=20)

    # 创建进度条
    progress_var = tk.DoubleVar(root)  # 使用root作为master
    progress_bar = ttk.Progressbar(progress_frame, length=window_width-40,
                                 mode='determinate', variable=progress_var)
    progress_bar.pack(pady=10)

    # 创建进度文本标签
    progress_label = tk.Label(progress_frame, text="正在启动中...",
                            font=("微软雅黑", 10), bg='white')
    progress_label.pack()

    # 更新启动窗口
    splash_root.update()

    # 模拟加载过程
    loading_steps = [
        ("正在初始化...", 20),
        ("加载系统组件...", 40),
        ("准备数据分析模块...", 60),
        ("配置用户界面...", 80),
        ("启动完成", 100)
    ]

    for text, progress in loading_steps:
        progress_label.config(text=text)
        progress_var.set(progress)
        splash_root.update()
        time.sleep(0.5)  # 模拟加载延迟

    # 设置主窗口标题
    root.title("中道体彩排列5分析软件")

    # 设置窗口图标
    try:
        root.iconbitmap("lottery.ico")
    except:
        pass

    # 设置窗口大小和位置
    window_width = 1200
    window_height = 800
    x = (screen_width - window_width) // 2
    y = (screen_height - window_height) // 2
    root.geometry(f"{window_width}x{window_height}+{x}+{y}")

    # 设置DPI感知
    try:
        from ctypes import windll
        windll.shcore.SetProcessDpiAwareness(1)
    except:
        pass

    # 创建应用实例
    app = Arrangement5LotteryApp(root)

    # 销毁启动窗口
    splash_root.destroy()

    # 显示主窗口
    root.deiconify()

    # 运行主程序
    root.mainloop()