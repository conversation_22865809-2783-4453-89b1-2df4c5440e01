#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示预测数据库分析的50个位置-数字组合
"""

import tkinter as tk
from tkinter import ttk
import random

def create_demo_window():
    """创建50组合分析演示窗口"""
    root = tk.Tk()
    root.title("预测数据库50组合分析演示")
    root.geometry("1000x700")
    
    # 创建主框架
    main_frame = ttk.Frame(root, padding="10")
    main_frame.pack(fill='both', expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="预测数据库分析范围：5位 × 10数字 = 50个组合", 
                           font=("微软雅黑", 16, "bold"))
    title_label.pack(pady=10)
    
    # 说明文本
    info_text = """
预测数据库功能会分析每个位置（一位到五位）每个数字（0-9）的最佳预测组合：
• 总共分析 5个位置 × 10个数字 = 50个位置-数字组合
• 每个组合都会测试前5期各位置的2-4元素组合（共15250种可能）
• 选择准确率最高的组合作为该位置-数字的最佳预测公式
• 所有分析都基于用户指定的期数范围内的数据
"""
    
    info_label = ttk.Label(main_frame, text=info_text, justify='left', 
                          font=("微软雅黑", 10))
    info_label.pack(pady=10, anchor='w')
    
    # 创建表格框架
    table_frame = ttk.LabelFrame(main_frame, text="50个位置-数字组合详细列表", padding="10")
    table_frame.pack(fill='both', expand=True, pady=10)
    
    # 创建表格
    columns = ('序号', '位置', '数字', '组合标识', '分析状态', '最佳组合示例', '准确率')
    tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
    
    # 设置列标题和宽度
    tree.heading('序号', text='序号')
    tree.column('序号', width=50)
    tree.heading('位置', text='位置')
    tree.column('位置', width=80)
    tree.heading('数字', text='数字')
    tree.column('数字', width=50)
    tree.heading('组合标识', text='组合标识')
    tree.column('组合标识', width=100)
    tree.heading('分析状态', text='分析状态')
    tree.column('分析状态', width=100)
    tree.heading('最佳组合示例', text='最佳组合示例')
    tree.column('最佳组合示例', width=200)
    tree.heading('准确率', text='准确率')
    tree.column('准确率', width=80)
    
    # 添加滚动条
    scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
    tree.configure(yscrollcommand=scrollbar.set)
    
    tree.pack(side='left', fill='both', expand=True)
    scrollbar.pack(side='right', fill='y')
    
    def generate_combinations():
        """生成50个组合的数据"""
        combinations = []
        positions = ['一位', '二位', '三位', '四位', '五位']
        
        index = 1
        for position in positions:
            for number in range(10):
                # 生成示例的最佳组合
                combo_examples = [
                    "上1期一位+上2期三位",
                    "上1期二位+上3期五位",
                    "上2期四位+上1期三位",
                    "上1期五位+上2期二位+上3期一位",
                    "上2期一位+上3期四位",
                    "上1期三位+上4期二位",
                    "上2期五位+上1期四位+上3期三位",
                    "上1期二位+上5期一位",
                ]
                
                best_combo = random.choice(combo_examples)
                accuracy = random.uniform(55.0, 85.0)
                
                combinations.append({
                    'index': index,
                    'position': position,
                    'number': number,
                    'combo_id': f"{position}-{number}",
                    'status': '待分析',
                    'best_combo': best_combo,
                    'accuracy': f"{accuracy:.1f}%"
                })
                index += 1
        
        return combinations
    
    def populate_table():
        """填充表格数据"""
        # 清空现有数据
        for item in tree.get_children():
            tree.delete(item)
        
        # 生成组合数据
        combinations = generate_combinations()
        
        # 添加数据到表格
        for combo in combinations:
            tree.insert('', 'end', values=(
                combo['index'],
                combo['position'],
                combo['number'],
                combo['combo_id'],
                combo['status'],
                combo['best_combo'],
                combo['accuracy']
            ))
    
    def simulate_analysis():
        """模拟分析过程"""
        # 获取所有项目
        items = tree.get_children()
        
        def update_item(index):
            if index >= len(items):
                # 分析完成
                status_label.config(text="✅ 50个组合分析完成！数据库已创建。")
                analyze_button.config(state='normal')
                return
            
            item = items[index]
            values = list(tree.item(item)['values'])
            
            # 更新状态为"分析中"
            values[4] = '分析中...'
            tree.item(item, values=values)
            tree.see(item)  # 滚动到当前项
            
            # 更新进度
            progress = ((index + 1) / len(items)) * 100
            progress_var.set(progress)
            status_label.config(text=f"正在分析第 {index + 1}/50 个组合：{values[3]}")
            
            root.update()
            
            # 模拟分析时间
            root.after(100, lambda: finish_item(index))
        
        def finish_item(index):
            item = items[index]
            values = list(tree.item(item)['values'])
            
            # 更新状态为"已完成"
            values[4] = '✅ 已完成'
            tree.item(item, values=values)
            
            # 继续下一个
            root.after(50, lambda: update_item(index + 1))
        
        # 开始分析
        analyze_button.config(state='disabled')
        reset_button.config(state='disabled')
        progress_var.set(0)
        update_item(0)
    
    def reset_analysis():
        """重置分析状态"""
        populate_table()
        progress_var.set(0)
        status_label.config(text="准备开始分析50个位置-数字组合...")
        analyze_button.config(state='normal')
        reset_button.config(state='normal')
    
    # 按钮框架
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(pady=10)
    
    analyze_button = ttk.Button(button_frame, text="模拟分析过程", command=simulate_analysis)
    analyze_button.pack(side='left', padx=5)
    
    reset_button = ttk.Button(button_frame, text="重置", command=reset_analysis)
    reset_button.pack(side='left', padx=5)
    
    ttk.Button(button_frame, text="关闭", command=root.destroy).pack(side='left', padx=5)
    
    # 进度条框架
    progress_frame = ttk.Frame(main_frame)
    progress_frame.pack(fill='x', pady=10)
    
    ttk.Label(progress_frame, text="分析进度:").pack(anchor='w')
    
    progress_var = tk.DoubleVar()
    progress_bar = ttk.Progressbar(progress_frame, length=400, mode='determinate', variable=progress_var)
    progress_bar.pack(fill='x', pady=5)
    
    status_label = ttk.Label(progress_frame, text="准备开始分析50个位置-数字组合...")
    status_label.pack(anchor='w')
    
    # 统计信息
    stats_text = """
分析统计信息：
• 总组合数：50个（5位 × 10数字）
• 每个组合测试的预测公式数：15,250个（前5期各位置的2-4元素组合）
• 总计算量：50 × 15,250 = 762,500次预测公式测试
• 数据库记录：每个组合保存1个最佳预测公式，共50条记录
• 分析基础：用户指定期数范围内的历史数据
"""
    
    stats_label = ttk.Label(main_frame, text=stats_text, justify='left', 
                           font=("微软雅黑", 9), foreground="blue")
    stats_label.pack(pady=10, anchor='w')
    
    # 初始化表格
    populate_table()
    
    return root

if __name__ == "__main__":
    root = create_demo_window()
    root.mainloop()
