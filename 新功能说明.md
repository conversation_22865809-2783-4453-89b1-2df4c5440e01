# 大小预测分析窗口新功能说明

## 概述

为大小预测分析窗口新增了两个重要功能：**预测数据库**和**检验组合**，用于自动化分析和验证预测组合的效果。

## 新增功能

### 1. 预测数据库按钮

#### 功能描述
- **基于用户指定的期数范围**自动遍历分析每个位置（一位到五位）每个数字（0-9）的所有可能预测组合
- **分析范围**：5个位置 × 10个数字 = **50个位置-数字组合**
- 在指定期数范围内找出每个位置-数字组合的准确率最高的预测组合
- 将最佳组合记录保存到SQLite数据库文件 `plwdx.db` 中
- 支持增量更新：如果数据库已存在，只添加缺失的记录
- **重要**：分析结果完全基于用户设置的期号范围（如最近1期到最近200期）

#### 技术实现
- 使用SQLite数据库替代Access文件（更好的跨平台兼容性）
- 数据库表结构：
  ```sql
  CREATE TABLE prediction_combinations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      position TEXT NOT NULL,           -- 位置（一位、二位等）
      number INTEGER NOT NULL,          -- 数字（0-9）
      combination_desc TEXT NOT NULL,   -- 组合描述
      combination_data TEXT NOT NULL,   -- 组合数据（JSON格式）
      accuracy REAL NOT NULL,           -- 准确率
      correct_count INTEGER NOT NULL,   -- 正确次数
      total_count INTEGER NOT NULL,     -- 总次数
      created_date TEXT NOT NULL,       -- 创建时间
      UNIQUE(position, number)          -- 唯一约束
  )
  ```

#### 遍历功能分析
1. **期数范围限定**：严格按照用户设置的期号范围（如最近1期到最近200期）进行分析
2. **位置遍历**：遍历五个位置（一位、二位、三位、四位、五位）
3. **数字遍历**：对每个位置遍历0-9共10个数字
4. **组合遍历**：遍历前5期所有位次的2-4元素组合
5. **数据遍历**：仅遍历指定期号范围内的历史数据
6. **准确率计算**：基于指定期数范围内的数据计算每个组合的预测准确率
7. **最优选择**：在指定期数范围内选择准确率最高的组合保存到数据库

#### 使用方法
1. 在大小预测分析窗口中**设置期号范围**（如：从最近1期到最近200期）
2. 点击"预测数据库"按钮
3. 系统自动开始分析指定期数范围内的数据，显示进度条
4. 可随时点击"停止分析"按钮中断操作
5. 分析完成后，基于指定期数的最佳组合保存到 `plwdx.db` 文件

### 2. 检验组合按钮

#### 功能描述
- 读取预测数据库中的最佳组合
- **基于用户指定的期号范围**对历史数据进行预测验证
- 计算并显示每期预测的成功率
- 统计各位置在指定期数范围内的总体成功率

#### 遍历功能分析
1. **期数范围限定**：严格按照用户设置的期号范围进行验证
2. **数据库遍历**：遍历数据库中所有预测组合记录
3. **历史数据遍历**：仅遍历指定期号范围内的每期数据
4. **位置遍历**：对每期数据的每个位置进行预测
5. **组合匹配**：根据当前位置的数字查找对应的最佳预测组合
6. **预测计算**：使用组合公式计算预测结果
7. **准确性验证**：将预测结果与实际下期数据对比
8. **统计汇总**：统计指定期数范围内总体和各位置的成功率

#### 使用方法
1. 确保已创建预测数据库
2. 在大小预测分析窗口中**设置期号范围**（验证范围）
3. 点击"检验组合"按钮
4. 系统自动开始验证指定期数范围内的数据，显示进度和结果
5. 可随时点击"停止分析"按钮中断操作

## 技术特点

### 1. 进度显示和控制
- 实时显示分析进度条
- 支持中途停止分析操作
- 按钮状态智能管理（分析时禁用其他按钮）

### 2. 数据持久化
- 使用SQLite数据库存储预测组合
- 支持数据的增量更新和查询
- JSON格式存储复杂的组合数据结构

### 3. 错误处理
- 完善的异常处理机制
- 用户友好的错误提示
- 数据验证和边界检查

### 4. 性能优化
- 智能跳过已存在的记录
- 批量数据库操作
- 内存使用优化

## 文件结构

```
项目目录/
├── 排列5分析软件精简版.py    # 主程序文件（已修改）
├── plwdx.db                   # 预测数据库文件（自动创建）
├── test_prediction_database.py # 数据库功能测试脚本
├── demo_new_features.py       # 新功能演示程序
└── 新功能说明.md             # 本说明文档
```

## 使用流程

1. **数据准备**
   - 运行主程序
   - 下载或导入彩票历史数据

2. **创建预测数据库**
   - 打开大小预测分析窗口
   - 设置合适的期号范围（建议200期以上）
   - 点击"预测数据库"按钮
   - 等待分析完成（可能需要较长时间）

3. **验证预测效果**
   - 设置验证的期号范围
   - 点击"检验组合"按钮
   - 查看预测成功率统计

4. **实际应用**
   - 使用数据库中的最佳组合进行实时预测
   - 根据当前期的数字查找对应组合
   - 计算下期预测范围

## 注意事项

1. **数据量要求**：建议使用至少200期的历史数据进行分析
2. **计算时间**：创建预测数据库可能需要较长时间，请耐心等待
3. **存储空间**：数据库文件大小约几MB，请确保有足够存储空间
4. **备份建议**：建议定期备份 `plwdx.db` 文件
5. **更新策略**：当有新的历史数据时，可重新运行预测数据库功能更新组合

## 扩展可能

1. **多种预测算法**：可以扩展支持不同的预测算法
2. **组合优化**：可以添加更多的组合筛选条件
3. **可视化分析**：可以添加图表显示预测效果
4. **导出功能**：可以添加预测结果的导出功能
5. **实时预测**：可以添加基于最新数据的实时预测功能
